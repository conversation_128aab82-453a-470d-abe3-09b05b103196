Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    gd32f4xx_it.o(i.SDIO_IRQHandler) refers to sdio_sdcard.o(i.sd_interrupts_process) for sd_interrupts_process
    gd32f4xx_it.o(i.SysTick_Handler) refers to systick.o(i.delay_decrement) for delay_decrement
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_flag_get) for usart_interrupt_flag_get
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_usart.o(i.usart_data_receive) for usart_data_receive
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_dma.o(i.dma_transfer_number_get) for dma_transfer_number_get
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to rt_memclr.o(.text) for __aeabi_memclr
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_dma.o(i.dma_transfer_number_config) for dma_transfer_number_config
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to mcu_cmic_gd32f470vet6.o(.bss) for rxbuffer
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to usart_app.o(.bss) for uart_dma_buffer
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to usart_app.o(.data) for rx_flag
    main.o(.rev16_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    main.o(.revsh_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    main.o(.rrx_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    main.o(i.fputc) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    main.o(i.fputc) refers to gd32f4xx_usart.o(i.usart_data_transmit) for usart_data_transmit
    main.o(i.fputc) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    main.o(i.main) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    main.o(i.main) refers to systick.o(i.systick_config) for systick_config
    main.o(i.main) refers to perf_counter.o(i.init_cycle_counter) for init_cycle_counter
    main.o(i.main) refers to perf_counter.o(i.delay_ms) for delay_ms
    main.o(i.main) refers to system_gd32f4xx.o(i.gd32f4xx_firmware_version_get) for gd32f4xx_firmware_version_get
    main.o(i.main) refers to mcu_cmic_gd32f470vet6.o(i.bsp_led_init) for bsp_led_init
    main.o(i.main) refers to mcu_cmic_gd32f470vet6.o(i.bsp_btn_init) for bsp_btn_init
    main.o(i.main) refers to mcu_cmic_gd32f470vet6.o(i.bsp_oled_init) for bsp_oled_init
    main.o(i.main) refers to mcu_cmic_gd32f470vet6.o(i.bsp_gd25qxx_init) for bsp_gd25qxx_init
    main.o(i.main) refers to mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) for bsp_usart_init
    main.o(i.main) refers to mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) for bsp_adc_init
    main.o(i.main) refers to mcu_cmic_gd32f470vet6.o(i.bsp_dac_init) for bsp_dac_init
    main.o(i.main) refers to mcu_cmic_gd32f470vet6.o(i.bsp_rtc_init) for bsp_rtc_init
    main.o(i.main) refers to sd_app.o(i.sd_fatfs_init) for sd_fatfs_init
    main.o(i.main) refers to btn_app.o(i.app_btn_init) for app_btn_init
    main.o(i.main) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.main) refers to gd25qxx.o(i.test_spi_flash) for test_spi_flash
    main.o(i.main) refers to sd_app.o(i.sd_fatfs_test) for sd_fatfs_test
    main.o(i.main) refers to scheduler.o(i.scheduler_init) for scheduler_init
    main.o(i.main) refers to scheduler.o(i.scheduler_run) for scheduler_run
    systick.o(i.delay_1ms) refers to systick.o(.data) for delay
    systick.o(i.delay_decrement) refers to systick.o(.data) for delay
    systick.o(i.systick_config) refers to systick.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    systick.o(i.systick_config) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    mcu_cmic_gd32f470vet6.o(.rev16_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    mcu_cmic_gd32f470vet6.o(.revsh_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    mcu_cmic_gd32f470vet6.o(.rrx_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_adc.o(i.adc_clock_config) for adc_clock_config
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_dma.o(i.dma_circulation_enable) for dma_circulation_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_adc.o(i.adc_sync_mode_config) for adc_sync_mode_config
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_adc.o(i.adc_special_function_config) for adc_special_function_config
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_adc.o(i.adc_data_alignment_config) for adc_data_alignment_config
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_adc.o(i.adc_channel_length_config) for adc_channel_length_config
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_adc.o(i.adc_routine_channel_config) for adc_routine_channel_config
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_adc.o(i.adc_external_trigger_source_config) for adc_external_trigger_source_config
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_adc.o(i.adc_external_trigger_config) for adc_external_trigger_config
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_adc.o(i.adc_dma_request_after_last_enable) for adc_dma_request_after_last_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_adc.o(i.adc_dma_mode_enable) for adc_dma_mode_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_adc.o(i.adc_enable) for adc_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to systick.o(i.delay_1ms) for delay_1ms
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_adc.o(i.adc_calibration_enable) for adc_calibration_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to gd32f4xx_adc.o(i.adc_software_trigger_enable) for adc_software_trigger_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_adc_init) refers to mcu_cmic_gd32f470vet6.o(.data) for adc_value
    mcu_cmic_gd32f470vet6.o(i.bsp_btn_init) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    mcu_cmic_gd32f470vet6.o(i.bsp_btn_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_btn_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    mcu_cmic_gd32f470vet6.o(i.bsp_dac_init) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    mcu_cmic_gd32f470vet6.o(i.bsp_dac_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_dac_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    mcu_cmic_gd32f470vet6.o(i.bsp_dac_init) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    mcu_cmic_gd32f470vet6.o(i.bsp_dac_init) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    mcu_cmic_gd32f470vet6.o(i.bsp_dac_init) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    mcu_cmic_gd32f470vet6.o(i.bsp_dac_init) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_dac_init) refers to gd32f4xx_dac.o(i.dac_deinit) for dac_deinit
    mcu_cmic_gd32f470vet6.o(i.bsp_dac_init) refers to gd32f4xx_dac.o(i.dac_trigger_source_config) for dac_trigger_source_config
    mcu_cmic_gd32f470vet6.o(i.bsp_dac_init) refers to gd32f4xx_dac.o(i.dac_trigger_enable) for dac_trigger_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_dac_init) refers to gd32f4xx_dac.o(i.dac_wave_mode_config) for dac_wave_mode_config
    mcu_cmic_gd32f470vet6.o(i.bsp_dac_init) refers to gd32f4xx_dac.o(i.dac_enable) for dac_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_dac_init) refers to gd32f4xx_dac.o(i.dac_dma_enable) for dac_dma_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_dac_init) refers to mcu_cmic_gd32f470vet6.o(i.timer5_config) for timer5_config
    mcu_cmic_gd32f470vet6.o(i.bsp_dac_init) refers to mcu_cmic_gd32f470vet6.o(.data) for convertarr
    mcu_cmic_gd32f470vet6.o(i.bsp_gd25qxx_init) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    mcu_cmic_gd32f470vet6.o(i.bsp_gd25qxx_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_gd25qxx_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    mcu_cmic_gd32f470vet6.o(i.bsp_gd25qxx_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    mcu_cmic_gd32f470vet6.o(i.bsp_gd25qxx_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    mcu_cmic_gd32f470vet6.o(i.bsp_gd25qxx_init) refers to gd32f4xx_spi.o(i.spi_init) for spi_init
    mcu_cmic_gd32f470vet6.o(i.bsp_gd25qxx_init) refers to gd25qxx.o(i.spi_flash_init) for spi_flash_init
    mcu_cmic_gd32f470vet6.o(i.bsp_led_init) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    mcu_cmic_gd32f470vet6.o(i.bsp_led_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_led_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    mcu_cmic_gd32f470vet6.o(i.bsp_led_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    mcu_cmic_gd32f470vet6.o(i.bsp_oled_init) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    mcu_cmic_gd32f470vet6.o(i.bsp_oled_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_oled_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    mcu_cmic_gd32f470vet6.o(i.bsp_oled_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    mcu_cmic_gd32f470vet6.o(i.bsp_oled_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    mcu_cmic_gd32f470vet6.o(i.bsp_oled_init) refers to gd32f4xx_i2c.o(i.i2c_clock_config) for i2c_clock_config
    mcu_cmic_gd32f470vet6.o(i.bsp_oled_init) refers to gd32f4xx_i2c.o(i.i2c_mode_addr_config) for i2c_mode_addr_config
    mcu_cmic_gd32f470vet6.o(i.bsp_oled_init) refers to gd32f4xx_i2c.o(i.i2c_enable) for i2c_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_oled_init) refers to gd32f4xx_i2c.o(i.i2c_ack_config) for i2c_ack_config
    mcu_cmic_gd32f470vet6.o(i.bsp_oled_init) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    mcu_cmic_gd32f470vet6.o(i.bsp_oled_init) refers to gd32f4xx_dma.o(i.dma_single_data_para_struct_init) for dma_single_data_para_struct_init
    mcu_cmic_gd32f470vet6.o(i.bsp_oled_init) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    mcu_cmic_gd32f470vet6.o(i.bsp_oled_init) refers to gd32f4xx_dma.o(i.dma_circulation_disable) for dma_circulation_disable
    mcu_cmic_gd32f470vet6.o(i.bsp_oled_init) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    mcu_cmic_gd32f470vet6.o(i.bsp_oled_init) refers to mcu_cmic_gd32f470vet6.o(.data) for oled_data_buf
    mcu_cmic_gd32f470vet6.o(i.bsp_rtc_init) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    mcu_cmic_gd32f470vet6.o(i.bsp_rtc_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_rtc_init) refers to gd32f4xx_pmu.o(i.pmu_backup_write_enable) for pmu_backup_write_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_rtc_init) refers to mcu_cmic_gd32f470vet6.o(i.bsp_rtc_pre_cfg) for bsp_rtc_pre_cfg
    mcu_cmic_gd32f470vet6.o(i.bsp_rtc_init) refers to mcu_cmic_gd32f470vet6.o(i.bsp_rtc_setup) for bsp_rtc_setup
    mcu_cmic_gd32f470vet6.o(i.bsp_rtc_init) refers to gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear) for rcu_all_reset_flag_clear
    mcu_cmic_gd32f470vet6.o(i.bsp_rtc_init) refers to mcu_cmic_gd32f470vet6.o(.data) for RTCSRC_FLAG
    mcu_cmic_gd32f470vet6.o(i.bsp_rtc_pre_cfg) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    mcu_cmic_gd32f470vet6.o(i.bsp_rtc_pre_cfg) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    mcu_cmic_gd32f470vet6.o(i.bsp_rtc_pre_cfg) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    mcu_cmic_gd32f470vet6.o(i.bsp_rtc_pre_cfg) refers to gd32f4xx_rcu.o(i.rcu_rtc_clock_config) for rcu_rtc_clock_config
    mcu_cmic_gd32f470vet6.o(i.bsp_rtc_pre_cfg) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_rtc_pre_cfg) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    mcu_cmic_gd32f470vet6.o(i.bsp_rtc_pre_cfg) refers to mcu_cmic_gd32f470vet6.o(.data) for prescaler_s
    mcu_cmic_gd32f470vet6.o(i.bsp_rtc_setup) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    mcu_cmic_gd32f470vet6.o(i.bsp_rtc_setup) refers to gd32f4xx_rtc.o(i.rtc_init) for rtc_init
    mcu_cmic_gd32f470vet6.o(i.bsp_rtc_setup) refers to mcu_cmic_gd32f470vet6.o(.data) for prescaler_a
    mcu_cmic_gd32f470vet6.o(i.bsp_rtc_setup) refers to mcu_cmic_gd32f470vet6.o(.bss) for rtc_initpara
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_dma.o(i.dma_circulation_disable) for dma_circulation_disable
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_usart.o(i.usart_deinit) for usart_deinit
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_usart.o(i.usart_receive_config) for usart_receive_config
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_usart.o(i.usart_transmit_config) for usart_transmit_config
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_usart.o(i.usart_dma_receive_config) for usart_dma_receive_config
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_usart.o(i.usart_enable) for usart_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to gd32f4xx_usart.o(i.usart_interrupt_enable) for usart_interrupt_enable
    mcu_cmic_gd32f470vet6.o(i.bsp_usart_init) refers to mcu_cmic_gd32f470vet6.o(.bss) for rxbuffer
    mcu_cmic_gd32f470vet6.o(i.timer5_config) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    mcu_cmic_gd32f470vet6.o(i.timer5_config) refers to gd32f4xx_timer.o(i.timer_deinit) for timer_deinit
    mcu_cmic_gd32f470vet6.o(i.timer5_config) refers to gd32f4xx_timer.o(i.timer_struct_para_init) for timer_struct_para_init
    mcu_cmic_gd32f470vet6.o(i.timer5_config) refers to gd32f4xx_timer.o(i.timer_init) for timer_init
    mcu_cmic_gd32f470vet6.o(i.timer5_config) refers to gd32f4xx_timer.o(i.timer_master_output_trigger_source_select) for timer_master_output_trigger_source_select
    mcu_cmic_gd32f470vet6.o(i.timer5_config) refers to gd32f4xx_timer.o(i.timer_enable) for timer_enable
    mcu_cmic_gd32f470vet6.o(.bss) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    mcu_cmic_gd32f470vet6.o(.data) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(.rev16_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(.revsh_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(.rrx_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(i.spi_flash_buffer_read) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(i.spi_flash_buffer_read) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd25qxx.o(i.spi_flash_buffer_read) refers to gd25qxx.o(i.spi_flash_send_byte_dma) for spi_flash_send_byte_dma
    gd25qxx.o(i.spi_flash_buffer_read) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd25qxx.o(i.spi_flash_buffer_write) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(i.spi_flash_buffer_write) refers to gd25qxx.o(i.spi_flash_page_write) for spi_flash_page_write
    gd25qxx.o(i.spi_flash_bulk_erase) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(i.spi_flash_bulk_erase) refers to gd25qxx.o(i.spi_flash_write_enable) for spi_flash_write_enable
    gd25qxx.o(i.spi_flash_bulk_erase) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd25qxx.o(i.spi_flash_bulk_erase) refers to gd25qxx.o(i.spi_flash_send_byte_dma) for spi_flash_send_byte_dma
    gd25qxx.o(i.spi_flash_bulk_erase) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd25qxx.o(i.spi_flash_bulk_erase) refers to gd25qxx.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    gd25qxx.o(i.spi_flash_init) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(i.spi_flash_init) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd25qxx.o(i.spi_flash_init) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    gd25qxx.o(i.spi_flash_page_write) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(i.spi_flash_page_write) refers to gd25qxx.o(i.spi_flash_write_enable) for spi_flash_write_enable
    gd25qxx.o(i.spi_flash_page_write) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd25qxx.o(i.spi_flash_page_write) refers to gd25qxx.o(i.spi_flash_send_byte_dma) for spi_flash_send_byte_dma
    gd25qxx.o(i.spi_flash_page_write) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd25qxx.o(i.spi_flash_page_write) refers to gd25qxx.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    gd25qxx.o(i.spi_flash_read_id) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(i.spi_flash_read_id) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd25qxx.o(i.spi_flash_read_id) refers to gd25qxx.o(i.spi_flash_send_byte_dma) for spi_flash_send_byte_dma
    gd25qxx.o(i.spi_flash_read_id) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd25qxx.o(i.spi_flash_sector_erase) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(i.spi_flash_sector_erase) refers to gd25qxx.o(i.spi_flash_write_enable) for spi_flash_write_enable
    gd25qxx.o(i.spi_flash_sector_erase) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd25qxx.o(i.spi_flash_sector_erase) refers to gd25qxx.o(i.spi_flash_send_byte_dma) for spi_flash_send_byte_dma
    gd25qxx.o(i.spi_flash_sector_erase) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd25qxx.o(i.spi_flash_sector_erase) refers to gd25qxx.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    gd25qxx.o(i.spi_flash_send_byte_dma) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_spi.o(i.spi_dma_enable) for spi_dma_enable
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_spi.o(i.spi_dma_disable) for spi_dma_disable
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to mcu_cmic_gd32f470vet6.o(.bss) for spi1_send_array
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_spi.o(i.spi_dma_enable) for spi_dma_enable
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_spi.o(i.spi_dma_disable) for spi_dma_disable
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to mcu_cmic_gd32f470vet6.o(.bss) for spi1_send_array
    gd25qxx.o(i.spi_flash_start_read_sequence) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(i.spi_flash_start_read_sequence) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd25qxx.o(i.spi_flash_start_read_sequence) refers to gd25qxx.o(i.spi_flash_send_byte_dma) for spi_flash_send_byte_dma
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_spi.o(i.spi_dma_enable) for spi_dma_enable
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_spi.o(i.spi_dma_disable) for spi_dma_disable
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to mcu_cmic_gd32f470vet6.o(.bss) for spi1_send_array
    gd25qxx.o(i.spi_flash_wait_for_dma_end) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(i.spi_flash_wait_for_dma_end) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    gd25qxx.o(i.spi_flash_wait_for_dma_end) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    gd25qxx.o(i.spi_flash_wait_for_write_end) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(i.spi_flash_wait_for_write_end) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd25qxx.o(i.spi_flash_wait_for_write_end) refers to gd25qxx.o(i.spi_flash_send_byte_dma) for spi_flash_send_byte_dma
    gd25qxx.o(i.spi_flash_wait_for_write_end) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd25qxx.o(i.spi_flash_write_enable) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(i.spi_flash_write_enable) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd25qxx.o(i.spi_flash_write_enable) refers to gd25qxx.o(i.spi_flash_send_byte_dma) for spi_flash_send_byte_dma
    gd25qxx.o(i.spi_flash_write_enable) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd25qxx.o(i.test_spi_flash) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    gd25qxx.o(i.test_spi_flash) refers to usart_app.o(i.my_printf) for my_printf
    gd25qxx.o(i.test_spi_flash) refers to gd25qxx.o(i.spi_flash_init) for spi_flash_init
    gd25qxx.o(i.test_spi_flash) refers to gd25qxx.o(i.spi_flash_read_id) for spi_flash_read_id
    gd25qxx.o(i.test_spi_flash) refers to strlen.o(.text) for strlen
    gd25qxx.o(i.test_spi_flash) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    gd25qxx.o(i.test_spi_flash) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    gd25qxx.o(i.test_spi_flash) refers to gd25qxx.o(i.spi_flash_buffer_read) for spi_flash_buffer_read
    gd25qxx.o(i.test_spi_flash) refers to memcmp.o(.text) for memcmp
    lfs.o(i.lfs_alignup) refers to lfs.o(i.lfs_aligndown) for lfs_aligndown
    lfs.o(i.lfs_alloc) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    lfs.o(i.lfs_alloc) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    lfs.o(i.lfs_alloc) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    lfs.o(i.lfs_alloc) refers to _printf_dec.o(.text) for _printf_int_dec
    lfs.o(i.lfs_alloc) refers to __2printf.o(.text) for __2printf
    lfs.o(i.lfs_alloc) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_alloc) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    lfs.o(i.lfs_alloc) refers to lfs.o(i.lfs_fs_traverse) for lfs_fs_traverse
    lfs.o(i.lfs_alloc) refers to lfs.o(i.lfs_alloc_lookahead) for lfs_alloc_lookahead
    lfs.o(i.lfs_bd_cmp) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_bd_erase) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_bd_flush) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_bd_flush) refers to lfs.o(i.lfs_alignup) for lfs_alignup
    lfs.o(i.lfs_bd_flush) refers to lfs.o(i.lfs_cache_drop) for lfs_cache_drop
    lfs.o(i.lfs_bd_flush) refers to lfs.o(i.lfs_bd_cmp) for lfs_bd_cmp
    lfs.o(i.lfs_bd_flush) refers to lfs.o(i.lfs_cache_zero) for lfs_cache_zero
    lfs.o(i.lfs_bd_prog) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_bd_prog) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_bd_prog) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    lfs.o(i.lfs_bd_prog) refers to lfs.o(i.lfs_max) for lfs_max
    lfs.o(i.lfs_bd_prog) refers to lfs.o(i.lfs_bd_flush) for lfs_bd_flush
    lfs.o(i.lfs_bd_prog) refers to lfs.o(i.lfs_aligndown) for lfs_aligndown
    lfs.o(i.lfs_bd_read) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_bd_read) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_bd_read) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    lfs.o(i.lfs_bd_read) refers to lfs.o(i.lfs_aligndown) for lfs_aligndown
    lfs.o(i.lfs_bd_read) refers to lfs.o(i.lfs_alignup) for lfs_alignup
    lfs.o(i.lfs_bd_sync) refers to lfs.o(i.lfs_cache_drop) for lfs_cache_drop
    lfs.o(i.lfs_bd_sync) refers to lfs.o(i.lfs_bd_flush) for lfs_bd_flush
    lfs.o(i.lfs_bd_sync) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_cache_zero) refers to aeabi_memset.o(.text) for __aeabi_memset
    lfs.o(i.lfs_commitattr) refers to lfs.o(i.lfs_dir_find) for lfs_dir_find
    lfs.o(i.lfs_commitattr) refers to lfs.o(i.lfs_tag_id) for lfs_tag_id
    lfs.o(i.lfs_commitattr) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_commitattr) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_ctz) refers to lfs.o(i.lfs_npw2) for lfs_npw2
    lfs.o(i.lfs_ctz_extend) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    lfs.o(i.lfs_ctz_extend) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    lfs.o(i.lfs_ctz_extend) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    lfs.o(i.lfs_ctz_extend) refers to _printf_dec.o(.text) for _printf_int_dec
    lfs.o(i.lfs_ctz_extend) refers to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    lfs.o(i.lfs_ctz_extend) refers to lfs.o(i.lfs_alloc) for lfs_alloc
    lfs.o(i.lfs_ctz_extend) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_ctz_extend) refers to lfs.o(i.lfs_bd_erase) for lfs_bd_erase
    lfs.o(i.lfs_ctz_extend) refers to lfs.o(i.lfs_ctz_index) for lfs_ctz_index
    lfs.o(i.lfs_ctz_extend) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_ctz_extend) refers to lfs.o(i.lfs_bd_prog) for lfs_bd_prog
    lfs.o(i.lfs_ctz_extend) refers to lfs.o(i.lfs_ctz) for lfs_ctz
    lfs.o(i.lfs_ctz_extend) refers to lfs.o(i.lfs_tole32) for lfs_tole32
    lfs.o(i.lfs_ctz_extend) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_ctz_extend) refers to __2printf.o(.text) for __2printf
    lfs.o(i.lfs_ctz_extend) refers to lfs.o(i.lfs_cache_drop) for lfs_cache_drop
    lfs.o(i.lfs_ctz_find) refers to lfs.o(i.lfs_ctz_index) for lfs_ctz_index
    lfs.o(i.lfs_ctz_find) refers to lfs.o(i.lfs_ctz) for lfs_ctz
    lfs.o(i.lfs_ctz_find) refers to lfs.o(i.lfs_npw2) for lfs_npw2
    lfs.o(i.lfs_ctz_find) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_ctz_find) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_ctz_find) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_ctz_find) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_ctz_fromle32) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_ctz_index) refers to lfs.o(i.lfs_popc) for lfs_popc
    lfs.o(i.lfs_ctz_tole32) refers to lfs.o(i.lfs_tole32) for lfs_tole32
    lfs.o(i.lfs_ctz_traverse) refers to lfs.o(i.lfs_ctz_index) for lfs_ctz_index
    lfs.o(i.lfs_ctz_traverse) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_ctz_traverse) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_deinit) refers to lfs.o(i.lfs_free) for lfs_free
    lfs.o(i.lfs_dir_alloc) refers to lfs.o(i.lfs_alloc) for lfs_alloc
    lfs.o(i.lfs_dir_alloc) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_dir_alloc) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_pair_cmp) for lfs_pair_cmp
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_file_outline) for lfs_file_outline
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_file_flush) for lfs_file_flush
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_tag_type3) for lfs_tag_type3
    lfs.o(i.lfs_dir_commit) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_tag_type1) for lfs_tag_type1
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_tag_chunk) for lfs_tag_chunk
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_gstate_hasmovehere) for lfs_gstate_hasmovehere
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_gstate_xormove) for lfs_gstate_xormove
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_tag_isvalid) for lfs_tag_isvalid
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_fs_pred) for lfs_fs_pred
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_dir_drop) for lfs_dir_drop
    lfs.o(i.lfs_dir_commit) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_pair_tole32) for lfs_pair_tole32
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_dir_traverse) for lfs_dir_traverse
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_gstate_iszero) for lfs_gstate_iszero
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_dir_getgstate) for lfs_dir_getgstate
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_gstate_tole32) for lfs_gstate_tole32
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_dir_commitattr) for lfs_dir_commitattr
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_gstate_fromle32) for lfs_gstate_fromle32
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_dir_commitcrc) for lfs_dir_commitcrc
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_cache_drop) for lfs_cache_drop
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_dir_compact) for lfs_dir_compact
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_tag_id) for lfs_tag_id
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_dir_commit) refers to lfs.o(.constdata) for .constdata
    lfs.o(i.lfs_dir_commit) refers to lfs.o(i.lfs_dir_commit_commit) for lfs_dir_commit_commit
    lfs.o(i.lfs_dir_commit_commit) refers to lfs.o(i.lfs_dir_commitattr) for lfs_dir_commitattr
    lfs.o(i.lfs_dir_commit_size) refers to lfs.o(i.lfs_tag_dsize) for lfs_tag_dsize
    lfs.o(i.lfs_dir_commitattr) refers to lfs.o(i.lfs_tag_dsize) for lfs_tag_dsize
    lfs.o(i.lfs_dir_commitattr) refers to lfs.o(i.lfs_tobe32) for lfs_tobe32
    lfs.o(i.lfs_dir_commitattr) refers to lfs.o(i.lfs_dir_commitprog) for lfs_dir_commitprog
    lfs.o(i.lfs_dir_commitattr) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_dir_commitcrc) refers to lfs.o(i.lfs_alignup) for lfs_alignup
    lfs.o(i.lfs_dir_commitcrc) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_dir_commitcrc) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_dir_commitcrc) refers to lfs.o(i.lfs_frombe32) for lfs_frombe32
    lfs.o(i.lfs_dir_commitcrc) refers to lfs.o(i.lfs_tobe32) for lfs_tobe32
    lfs.o(i.lfs_dir_commitcrc) refers to lfs_util.o(i.lfs_crc) for lfs_crc
    lfs.o(i.lfs_dir_commitcrc) refers to lfs.o(i.lfs_tole32) for lfs_tole32
    lfs.o(i.lfs_dir_commitcrc) refers to lfs.o(i.lfs_bd_prog) for lfs_bd_prog
    lfs.o(i.lfs_dir_commitcrc) refers to lfs.o(i.lfs_tag_size) for lfs_tag_size
    lfs.o(i.lfs_dir_commitcrc) refers to lfs.o(i.lfs_bd_sync) for lfs_bd_sync
    lfs.o(i.lfs_dir_commitprog) refers to lfs.o(i.lfs_bd_prog) for lfs_bd_prog
    lfs.o(i.lfs_dir_commitprog) refers to lfs_util.o(i.lfs_crc) for lfs_crc
    lfs.o(i.lfs_dir_compact) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    lfs.o(i.lfs_dir_compact) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    lfs.o(i.lfs_dir_compact) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    lfs.o(i.lfs_dir_compact) refers to _printf_dec.o(.text) for _printf_int_dec
    lfs.o(i.lfs_dir_compact) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    lfs.o(i.lfs_dir_compact) refers to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_dir_traverse) for lfs_dir_traverse
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_alignup) for lfs_alignup
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_dir_split) for lfs_dir_split
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_pair_cmp) for lfs_pair_cmp
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_fs_size) for lfs_fs_size
    lfs.o(i.lfs_dir_compact) refers to __2printf.o(.text) for __2printf
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_dir_getgstate) for lfs_dir_getgstate
    lfs.o(i.lfs_dir_compact) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_bd_erase) for lfs_bd_erase
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_tole32) for lfs_tole32
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_dir_commitprog) for lfs_dir_commitprog
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_pair_isnull) for lfs_pair_isnull
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_pair_tole32) for lfs_pair_tole32
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_dir_commitattr) for lfs_dir_commitattr
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_gstate_iszero) for lfs_gstate_iszero
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_gstate_tole32) for lfs_gstate_tole32
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_gstate_fromle32) for lfs_gstate_fromle32
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_dir_commitcrc) for lfs_dir_commitcrc
    lfs.o(i.lfs_dir_compact) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_pair_swap) for lfs_pair_swap
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_gstate_hasmovehere) for lfs_gstate_hasmovehere
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_gstate_xormove) for lfs_gstate_xormove
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_cache_drop) for lfs_cache_drop
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_alloc) for lfs_alloc
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_fs_relocate) for lfs_fs_relocate
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_dir_commit_size) for lfs_dir_commit_size
    lfs.o(i.lfs_dir_compact) refers to lfs.o(.constdata) for .constdata
    lfs.o(i.lfs_dir_compact) refers to lfs.o(i.lfs_dir_commit_commit) for lfs_dir_commit_commit
    lfs.o(i.lfs_dir_drop) refers to lfs.o(i.lfs_dir_getgstate) for lfs_dir_getgstate
    lfs.o(i.lfs_dir_drop) refers to lfs.o(i.lfs_pair_tole32) for lfs_pair_tole32
    lfs.o(i.lfs_dir_drop) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_dir_drop) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_dir_fetch) refers to lfs.o(i.lfs_dir_fetchmatch) for lfs_dir_fetchmatch
    lfs.o(i.lfs_dir_fetchmatch) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    lfs.o(i.lfs_dir_fetchmatch) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    lfs.o(i.lfs_dir_fetchmatch) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    lfs.o(i.lfs_dir_fetchmatch) refers to _printf_dec.o(.text) for _printf_int_dec
    lfs.o(i.lfs_dir_fetchmatch) refers to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_tole32) for lfs_tole32
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs_util.o(i.lfs_crc) for lfs_crc
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_tag_dsize) for lfs_tag_dsize
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_frombe32) for lfs_frombe32
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_tag_isvalid) for lfs_tag_isvalid
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_tag_type1) for lfs_tag_type1
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_tag_chunk) for lfs_tag_chunk
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_tag_id) for lfs_tag_id
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_tag_splice) for lfs_tag_splice
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_gstate_hasmovehere) for lfs_gstate_hasmovehere
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(i.lfs_pair_swap) for lfs_pair_swap
    lfs.o(i.lfs_dir_fetchmatch) refers to __2printf.o(.text) for __2printf
    lfs.o(i.lfs_dir_fetchmatch) refers to lfs.o(.constdata) for .constdata
    lfs.o(i.lfs_dir_find) refers to strspn.o(.text) for strspn
    lfs.o(i.lfs_dir_find) refers to strcspn.o(.text) for strcspn
    lfs.o(i.lfs_dir_find) refers to memcmp.o(.text) for memcmp
    lfs.o(i.lfs_dir_find) refers to lfs.o(i.lfs_tag_type3) for lfs_tag_type3
    lfs.o(i.lfs_dir_find) refers to lfs.o(i.lfs_tag_id) for lfs_tag_id
    lfs.o(i.lfs_dir_find) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_dir_find) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_dir_find) refers to strchr.o(.text) for strchr
    lfs.o(i.lfs_dir_find) refers to lfs.o(i.lfs_dir_fetchmatch) for lfs_dir_fetchmatch
    lfs.o(i.lfs_dir_find) refers to lfs.o(i.lfs_dir_find_match) for lfs_dir_find_match
    lfs.o(i.lfs_dir_find_match) refers to lfs.o(i.lfs_tag_size) for lfs_tag_size
    lfs.o(i.lfs_dir_find_match) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_dir_find_match) refers to lfs.o(i.lfs_bd_cmp) for lfs_bd_cmp
    lfs.o(i.lfs_dir_get) refers to lfs.o(i.lfs_tag_size) for lfs_tag_size
    lfs.o(i.lfs_dir_get) refers to lfs.o(i.lfs_dir_getslice) for lfs_dir_getslice
    lfs.o(i.lfs_dir_getgstate) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_dir_getgstate) refers to lfs.o(i.lfs_gstate_fromle32) for lfs_gstate_fromle32
    lfs.o(i.lfs_dir_getinfo) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_dir_getinfo) refers to lfs.o(i.lfs_tag_type3) for lfs_tag_type3
    lfs.o(i.lfs_dir_getinfo) refers to lfs.o(i.lfs_ctz_fromle32) for lfs_ctz_fromle32
    lfs.o(i.lfs_dir_getinfo) refers to lfs.o(i.lfs_tag_size) for lfs_tag_size
    lfs.o(i.lfs_dir_getread) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_dir_getread) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    lfs.o(i.lfs_dir_getread) refers to lfs.o(i.lfs_aligndown) for lfs_aligndown
    lfs.o(i.lfs_dir_getread) refers to lfs.o(i.lfs_alignup) for lfs_alignup
    lfs.o(i.lfs_dir_getread) refers to lfs.o(i.lfs_dir_getslice) for lfs_dir_getslice
    lfs.o(i.lfs_dir_getslice) refers to lfs.o(i.lfs_gstate_hasmovehere) for lfs_gstate_hasmovehere
    lfs.o(i.lfs_dir_getslice) refers to lfs.o(i.lfs_tag_id) for lfs_tag_id
    lfs.o(i.lfs_dir_getslice) refers to lfs.o(i.lfs_tag_dsize) for lfs_tag_dsize
    lfs.o(i.lfs_dir_getslice) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_dir_getslice) refers to lfs.o(i.lfs_frombe32) for lfs_frombe32
    lfs.o(i.lfs_dir_getslice) refers to lfs.o(i.lfs_tag_type1) for lfs_tag_type1
    lfs.o(i.lfs_dir_getslice) refers to lfs.o(i.lfs_tag_splice) for lfs_tag_splice
    lfs.o(i.lfs_dir_getslice) refers to lfs.o(i.lfs_tag_isdelete) for lfs_tag_isdelete
    lfs.o(i.lfs_dir_getslice) refers to lfs.o(i.lfs_tag_size) for lfs_tag_size
    lfs.o(i.lfs_dir_getslice) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_dir_getslice) refers to rt_memclr.o(.text) for __aeabi_memclr
    lfs.o(i.lfs_dir_open) refers to lfs.o(i.lfs_dir_find) for lfs_dir_find
    lfs.o(i.lfs_dir_open) refers to lfs.o(i.lfs_tag_type3) for lfs_tag_type3
    lfs.o(i.lfs_dir_open) refers to lfs.o(i.lfs_tag_id) for lfs_tag_id
    lfs.o(i.lfs_dir_open) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_dir_open) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_dir_open) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_dir_read) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    lfs.o(i.lfs_dir_read) refers to strcpy.o(.text) for strcpy
    lfs.o(i.lfs_dir_read) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_dir_read) refers to lfs.o(i.lfs_dir_getinfo) for lfs_dir_getinfo
    lfs.o(i.lfs_dir_rewind) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_dir_seek) refers to lfs.o(i.lfs_dir_rewind) for lfs_dir_rewind
    lfs.o(i.lfs_dir_seek) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_dir_seek) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_dir_split) refers to lfs.o(i.lfs_dir_alloc) for lfs_dir_alloc
    lfs.o(i.lfs_dir_split) refers to lfs.o(i.lfs_dir_compact) for lfs_dir_compact
    lfs.o(i.lfs_dir_split) refers to lfs.o(i.lfs_pair_cmp) for lfs_pair_cmp
    lfs.o(i.lfs_dir_traverse) refers to lfs.o(i.lfs_tag_dsize) for lfs_tag_dsize
    lfs.o(i.lfs_dir_traverse) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_dir_traverse) refers to lfs.o(i.lfs_frombe32) for lfs_frombe32
    lfs.o(i.lfs_dir_traverse) refers to lfs.o(i.lfs_gstate_hasmovehere) for lfs_gstate_hasmovehere
    lfs.o(i.lfs_dir_traverse) refers to lfs.o(i.lfs_tag_id) for lfs_tag_id
    lfs.o(i.lfs_dir_traverse) refers to lfs.o(i.lfs_tag_type3) for lfs_tag_type3
    lfs.o(i.lfs_dir_traverse) refers to lfs.o(i.lfs_tag_size) for lfs_tag_size
    lfs.o(i.lfs_dir_traverse) refers to lfs.o(i.lfs_dir_traverse_filter) for lfs_dir_traverse_filter
    lfs.o(i.lfs_dir_traverse_filter) refers to lfs.o(i.lfs_tag_isdelete) for lfs_tag_isdelete
    lfs.o(i.lfs_dir_traverse_filter) refers to lfs.o(i.lfs_tag_type1) for lfs_tag_type1
    lfs.o(i.lfs_dir_traverse_filter) refers to lfs.o(i.lfs_tag_id) for lfs_tag_id
    lfs.o(i.lfs_dir_traverse_filter) refers to lfs.o(i.lfs_tag_splice) for lfs_tag_splice
    lfs.o(i.lfs_file_close) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_file_close) refers to lfs.o(i.lfs_file_sync) for lfs_file_sync
    lfs.o(i.lfs_file_close) refers to lfs.o(i.lfs_free) for lfs_free
    lfs.o(i.lfs_file_flush) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    lfs.o(i.lfs_file_flush) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    lfs.o(i.lfs_file_flush) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    lfs.o(i.lfs_file_flush) refers to _printf_dec.o(.text) for _printf_int_dec
    lfs.o(i.lfs_file_flush) refers to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    lfs.o(i.lfs_file_flush) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_file_flush) refers to lfs.o(i.lfs_cache_drop) for lfs_cache_drop
    lfs.o(i.lfs_file_flush) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    lfs.o(i.lfs_file_flush) refers to lfs.o(i.lfs_file_read) for lfs_file_read
    lfs.o(i.lfs_file_flush) refers to lfs.o(i.lfs_file_write) for lfs_file_write
    lfs.o(i.lfs_file_flush) refers to lfs.o(i.lfs_bd_flush) for lfs_bd_flush
    lfs.o(i.lfs_file_flush) refers to __2printf.o(.text) for __2printf
    lfs.o(i.lfs_file_flush) refers to lfs.o(i.lfs_file_relocate) for lfs_file_relocate
    lfs.o(i.lfs_file_flush) refers to lfs.o(i.lfs_max) for lfs_max
    lfs.o(i.lfs_file_flush) refers to lfs.o(.constdata) for .constdata
    lfs.o(i.lfs_file_open) refers to lfs.o(i.lfs_file_opencfg) for lfs_file_opencfg
    lfs.o(i.lfs_file_open) refers to lfs.o(.constdata) for defaults
    lfs.o(i.lfs_file_opencfg) refers to lfs.o(i.lfs_fs_forceconsistency) for lfs_fs_forceconsistency
    lfs.o(i.lfs_file_opencfg) refers to lfs.o(i.lfs_dir_find) for lfs_dir_find
    lfs.o(i.lfs_file_opencfg) refers to strlen.o(.text) for strlen
    lfs.o(i.lfs_file_opencfg) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    lfs.o(i.lfs_file_opencfg) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_file_opencfg) refers to lfs.o(i.lfs_tag_type3) for lfs_tag_type3
    lfs.o(i.lfs_file_opencfg) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_file_opencfg) refers to lfs.o(i.lfs_ctz_fromle32) for lfs_ctz_fromle32
    lfs.o(i.lfs_file_opencfg) refers to lfs.o(i.lfs_malloc) for lfs_malloc
    lfs.o(i.lfs_file_opencfg) refers to lfs.o(i.lfs_cache_zero) for lfs_cache_zero
    lfs.o(i.lfs_file_opencfg) refers to lfs.o(i.lfs_tag_size) for lfs_tag_size
    lfs.o(i.lfs_file_opencfg) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_file_opencfg) refers to lfs.o(i.lfs_file_close) for lfs_file_close
    lfs.o(i.lfs_file_outline) refers to lfs.o(i.lfs_alloc_ack) for lfs_alloc_ack
    lfs.o(i.lfs_file_outline) refers to lfs.o(i.lfs_file_relocate) for lfs_file_relocate
    lfs.o(i.lfs_file_read) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_file_read) refers to lfs.o(i.lfs_file_flush) for lfs_file_flush
    lfs.o(i.lfs_file_read) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_file_read) refers to lfs.o(i.lfs_ctz_find) for lfs_ctz_find
    lfs.o(i.lfs_file_read) refers to lfs.o(i.lfs_dir_getread) for lfs_dir_getread
    lfs.o(i.lfs_file_read) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_file_relocate) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    lfs.o(i.lfs_file_relocate) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    lfs.o(i.lfs_file_relocate) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    lfs.o(i.lfs_file_relocate) refers to _printf_dec.o(.text) for _printf_int_dec
    lfs.o(i.lfs_file_relocate) refers to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    lfs.o(i.lfs_file_relocate) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_file_relocate) refers to lfs.o(i.lfs_alloc) for lfs_alloc
    lfs.o(i.lfs_file_relocate) refers to lfs.o(i.lfs_bd_erase) for lfs_bd_erase
    lfs.o(i.lfs_file_relocate) refers to lfs.o(i.lfs_dir_getread) for lfs_dir_getread
    lfs.o(i.lfs_file_relocate) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_file_relocate) refers to lfs.o(i.lfs_bd_prog) for lfs_bd_prog
    lfs.o(i.lfs_file_relocate) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    lfs.o(i.lfs_file_relocate) refers to lfs.o(i.lfs_cache_zero) for lfs_cache_zero
    lfs.o(i.lfs_file_relocate) refers to __2printf.o(.text) for __2printf
    lfs.o(i.lfs_file_relocate) refers to lfs.o(i.lfs_cache_drop) for lfs_cache_drop
    lfs.o(i.lfs_file_rewind) refers to lfs.o(i.lfs_file_seek) for lfs_file_seek
    lfs.o(i.lfs_file_seek) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_file_seek) refers to lfs.o(i.lfs_file_flush) for lfs_file_flush
    lfs.o(i.lfs_file_size) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_file_size) refers to lfs.o(i.lfs_max) for lfs_max
    lfs.o(i.lfs_file_sync) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_file_sync) refers to lfs.o(i.lfs_file_flush) for lfs_file_flush
    lfs.o(i.lfs_file_sync) refers to lfs.o(i.lfs_pair_isnull) for lfs_pair_isnull
    lfs.o(i.lfs_file_sync) refers to lfs.o(i.lfs_ctz_tole32) for lfs_ctz_tole32
    lfs.o(i.lfs_file_sync) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_file_sync) refers to lfs.o(i.lfs_file_outline) for lfs_file_outline
    lfs.o(i.lfs_file_tell) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_file_truncate) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_file_truncate) refers to lfs.o(i.lfs_file_size) for lfs_file_size
    lfs.o(i.lfs_file_truncate) refers to lfs.o(i.lfs_file_flush) for lfs_file_flush
    lfs.o(i.lfs_file_truncate) refers to lfs.o(i.lfs_ctz_find) for lfs_ctz_find
    lfs.o(i.lfs_file_truncate) refers to lfs.o(i.lfs_file_seek) for lfs_file_seek
    lfs.o(i.lfs_file_truncate) refers to lfs.o(i.lfs_file_write) for lfs_file_write
    lfs.o(i.lfs_file_write) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_file_write) refers to lfs.o(i.lfs_file_flush) for lfs_file_flush
    lfs.o(i.lfs_file_write) refers to lfs.o(i.lfs_max) for lfs_max
    lfs.o(i.lfs_file_write) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_file_write) refers to lfs.o(i.lfs_file_outline) for lfs_file_outline
    lfs.o(i.lfs_file_write) refers to lfs.o(i.lfs_ctz_find) for lfs_ctz_find
    lfs.o(i.lfs_file_write) refers to lfs.o(i.lfs_cache_zero) for lfs_cache_zero
    lfs.o(i.lfs_file_write) refers to lfs.o(i.lfs_alloc_ack) for lfs_alloc_ack
    lfs.o(i.lfs_file_write) refers to lfs.o(i.lfs_ctz_extend) for lfs_ctz_extend
    lfs.o(i.lfs_file_write) refers to lfs.o(i.lfs_bd_prog) for lfs_bd_prog
    lfs.o(i.lfs_file_write) refers to lfs.o(i.lfs_file_relocate) for lfs_file_relocate
    lfs.o(i.lfs_format) refers to lfs.o(i.lfs_init) for lfs_init
    lfs.o(i.lfs_format) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    lfs.o(i.lfs_format) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_format) refers to lfs.o(i.lfs_alloc_ack) for lfs_alloc_ack
    lfs.o(i.lfs_format) refers to lfs.o(i.lfs_dir_alloc) for lfs_dir_alloc
    lfs.o(i.lfs_format) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    lfs.o(i.lfs_format) refers to lfs.o(i.lfs_superblock_tole32) for lfs_superblock_tole32
    lfs.o(i.lfs_format) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_format) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_format) refers to lfs.o(i.lfs_deinit) for lfs_deinit
    lfs.o(i.lfs_format) refers to lfs.o(.constdata) for .constdata
    lfs.o(i.lfs_free) refers to h1_free.o(.text) for free
    lfs.o(i.lfs_fs_demove) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    lfs.o(i.lfs_fs_demove) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    lfs.o(i.lfs_fs_demove) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    lfs.o(i.lfs_fs_demove) refers to _printf_dec.o(.text) for _printf_int_dec
    lfs.o(i.lfs_fs_demove) refers to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    lfs.o(i.lfs_fs_demove) refers to lfs.o(i.lfs_gstate_hasmove) for lfs_gstate_hasmove
    lfs.o(i.lfs_fs_demove) refers to lfs.o(i.lfs_tag_id) for lfs_tag_id
    lfs.o(i.lfs_fs_demove) refers to __2printf.o(.text) for __2printf
    lfs.o(i.lfs_fs_demove) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_fs_demove) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_fs_deorphan) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    lfs.o(i.lfs_fs_deorphan) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    lfs.o(i.lfs_fs_deorphan) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    lfs.o(i.lfs_fs_deorphan) refers to _printf_dec.o(.text) for _printf_int_dec
    lfs.o(i.lfs_fs_deorphan) refers to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(i.lfs_gstate_hasorphans) for lfs_gstate_hasorphans
    lfs.o(i.lfs_fs_deorphan) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(i.lfs_fs_parent) for lfs_fs_parent
    lfs.o(i.lfs_fs_deorphan) refers to __2printf.o(.text) for __2printf
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(i.lfs_dir_drop) for lfs_dir_drop
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(i.lfs_pair_tole32) for lfs_pair_tole32
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(i.lfs_pair_isnull) for lfs_pair_isnull
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(i.lfs_tag_size) for lfs_tag_size
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(i.lfs_fs_preporphans) for lfs_fs_preporphans
    lfs.o(i.lfs_fs_deorphan) refers to lfs.o(.constdata) for .constdata
    lfs.o(i.lfs_fs_forceconsistency) refers to lfs.o(i.lfs_fs_demove) for lfs_fs_demove
    lfs.o(i.lfs_fs_forceconsistency) refers to lfs.o(i.lfs_fs_deorphan) for lfs_fs_deorphan
    lfs.o(i.lfs_fs_parent) refers to lfs.o(i.lfs_dir_fetchmatch) for lfs_dir_fetchmatch
    lfs.o(i.lfs_fs_parent) refers to lfs.o(i.lfs_pair_isnull) for lfs_pair_isnull
    lfs.o(i.lfs_fs_parent) refers to lfs.o(i.lfs_fs_parent_match) for lfs_fs_parent_match
    lfs.o(i.lfs_fs_parent_match) refers to lfs.o(i.lfs_bd_read) for lfs_bd_read
    lfs.o(i.lfs_fs_parent_match) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_fs_parent_match) refers to lfs.o(i.lfs_pair_cmp) for lfs_pair_cmp
    lfs.o(i.lfs_fs_pred) refers to lfs.o(i.lfs_pair_cmp) for lfs_pair_cmp
    lfs.o(i.lfs_fs_pred) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_fs_pred) refers to lfs.o(i.lfs_pair_isnull) for lfs_pair_isnull
    lfs.o(i.lfs_fs_prepmove) refers to lfs.o(i.lfs_gstate_xormove) for lfs_gstate_xormove
    lfs.o(i.lfs_fs_preporphans) refers to lfs.o(i.lfs_gstate_hasorphans) for lfs_gstate_hasorphans
    lfs.o(i.lfs_fs_preporphans) refers to lfs.o(i.lfs_gstate_xororphans) for lfs_gstate_xororphans
    lfs.o(i.lfs_fs_relocate) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    lfs.o(i.lfs_fs_relocate) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    lfs.o(i.lfs_fs_relocate) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    lfs.o(i.lfs_fs_relocate) refers to _printf_dec.o(.text) for _printf_int_dec
    lfs.o(i.lfs_fs_relocate) refers to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    lfs.o(i.lfs_fs_relocate) refers to lfs.o(i.lfs_pair_cmp) for lfs_pair_cmp
    lfs.o(i.lfs_fs_relocate) refers to __2printf.o(.text) for __2printf
    lfs.o(i.lfs_fs_relocate) refers to lfs.o(i.lfs_fs_parent) for lfs_fs_parent
    lfs.o(i.lfs_fs_relocate) refers to lfs.o(i.lfs_fs_preporphans) for lfs_fs_preporphans
    lfs.o(i.lfs_fs_relocate) refers to lfs.o(i.lfs_pair_tole32) for lfs_pair_tole32
    lfs.o(i.lfs_fs_relocate) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_fs_relocate) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_fs_relocate) refers to lfs.o(i.lfs_fs_pred) for lfs_fs_pred
    lfs.o(i.lfs_fs_size) refers to lfs.o(i.lfs_fs_traverse) for lfs_fs_traverse
    lfs.o(i.lfs_fs_size) refers to lfs.o(i.lfs_fs_size_count) for lfs_fs_size_count
    lfs.o(i.lfs_fs_traverse) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    lfs.o(i.lfs_fs_traverse) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_fs_traverse) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_fs_traverse) refers to lfs.o(i.lfs_ctz_fromle32) for lfs_ctz_fromle32
    lfs.o(i.lfs_fs_traverse) refers to lfs.o(i.lfs_tag_type3) for lfs_tag_type3
    lfs.o(i.lfs_fs_traverse) refers to lfs.o(i.lfs_ctz_traverse) for lfs_ctz_traverse
    lfs.o(i.lfs_fs_traverse) refers to lfs.o(i.lfs_pair_isnull) for lfs_pair_isnull
    lfs.o(i.lfs_fs_traverse) refers to lfs.o(.constdata) for .constdata
    lfs.o(i.lfs_getattr) refers to lfs.o(i.lfs_dir_find) for lfs_dir_find
    lfs.o(i.lfs_getattr) refers to lfs.o(i.lfs_tag_id) for lfs_tag_id
    lfs.o(i.lfs_getattr) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_getattr) refers to lfs.o(i.lfs_min) for lfs_min
    lfs.o(i.lfs_getattr) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_getattr) refers to lfs.o(i.lfs_tag_size) for lfs_tag_size
    lfs.o(i.lfs_gstate_fromle32) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_gstate_hasmove) refers to lfs.o(i.lfs_tag_type1) for lfs_tag_type1
    lfs.o(i.lfs_gstate_hasmovehere) refers to lfs.o(i.lfs_tag_type1) for lfs_tag_type1
    lfs.o(i.lfs_gstate_hasmovehere) refers to lfs.o(i.lfs_pair_cmp) for lfs_pair_cmp
    lfs.o(i.lfs_gstate_hasorphans) refers to lfs.o(i.lfs_tag_size) for lfs_tag_size
    lfs.o(i.lfs_gstate_tole32) refers to lfs.o(i.lfs_tole32) for lfs_tole32
    lfs.o(i.lfs_init) refers to assert.o(.text) for __aeabi_assert
    lfs.o(i.lfs_init) refers to lfs.o(i.lfs_npw2) for lfs_npw2
    lfs.o(i.lfs_init) refers to lfs.o(i.lfs_malloc) for lfs_malloc
    lfs.o(i.lfs_init) refers to lfs.o(i.lfs_cache_zero) for lfs_cache_zero
    lfs.o(i.lfs_init) refers to lfs.o(i.lfs_deinit) for lfs_deinit
    lfs.o(i.lfs_init) refers to lfs.o(.conststring) for .conststring
    lfs.o(i.lfs_init) refers to lfs.o(.constdata) for <Data3>
    lfs.o(i.lfs_malloc) refers to h1_alloc.o(.text) for malloc
    lfs.o(i.lfs_mkdir) refers to lfs.o(i.lfs_fs_forceconsistency) for lfs_fs_forceconsistency
    lfs.o(i.lfs_mkdir) refers to lfs.o(i.lfs_dir_find) for lfs_dir_find
    lfs.o(i.lfs_mkdir) refers to strlen.o(.text) for strlen
    lfs.o(i.lfs_mkdir) refers to lfs.o(i.lfs_alloc_ack) for lfs_alloc_ack
    lfs.o(i.lfs_mkdir) refers to lfs.o(i.lfs_dir_alloc) for lfs_dir_alloc
    lfs.o(i.lfs_mkdir) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    lfs.o(i.lfs_mkdir) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_mkdir) refers to lfs.o(i.lfs_pair_tole32) for lfs_pair_tole32
    lfs.o(i.lfs_mkdir) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_mkdir) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_mkdir) refers to lfs.o(i.lfs_fs_preporphans) for lfs_fs_preporphans
    lfs.o(i.lfs_mkdir) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    lfs.o(i.lfs_mkdir) refers to lfs.o(.constdata) for .constdata
    lfs.o(i.lfs_mount) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    lfs.o(i.lfs_mount) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    lfs.o(i.lfs_mount) refers to _printf_dec.o(.text) for _printf_int_dec
    lfs.o(i.lfs_mount) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    lfs.o(i.lfs_mount) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    lfs.o(i.lfs_mount) refers to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_init) for lfs_init
    lfs.o(i.lfs_mount) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_dir_fetchmatch) for lfs_dir_fetchmatch
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_tag_isdelete) for lfs_tag_isdelete
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_superblock_fromle32) for lfs_superblock_fromle32
    lfs.o(i.lfs_mount) refers to __2printf.o(.text) for __2printf
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_dir_getgstate) for lfs_dir_getgstate
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_pair_isnull) for lfs_pair_isnull
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_tag_isvalid) for lfs_tag_isvalid
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_gstate_hasmove) for lfs_gstate_hasmove
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_tag_id) for lfs_tag_id
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_alloc_ack) for lfs_alloc_ack
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_unmount) for lfs_unmount
    lfs.o(i.lfs_mount) refers to lfs.o(.constdata) for .constdata
    lfs.o(i.lfs_mount) refers to lfs.o(i.lfs_dir_find_match) for lfs_dir_find_match
    lfs.o(i.lfs_pair_fromle32) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_pair_tole32) refers to lfs.o(i.lfs_tole32) for lfs_tole32
    lfs.o(i.lfs_popc) refers to lfs.o(i.__ARM_pop) for __ARM_pop
    lfs.o(i.lfs_remove) refers to lfs.o(i.lfs_fs_forceconsistency) for lfs_fs_forceconsistency
    lfs.o(i.lfs_remove) refers to lfs.o(i.lfs_dir_find) for lfs_dir_find
    lfs.o(i.lfs_remove) refers to lfs.o(i.lfs_tag_id) for lfs_tag_id
    lfs.o(i.lfs_remove) refers to lfs.o(i.lfs_tag_type3) for lfs_tag_type3
    lfs.o(i.lfs_remove) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_remove) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_remove) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_remove) refers to lfs.o(i.lfs_fs_preporphans) for lfs_fs_preporphans
    lfs.o(i.lfs_remove) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_remove) refers to lfs.o(i.lfs_fs_pred) for lfs_fs_pred
    lfs.o(i.lfs_remove) refers to lfs.o(i.lfs_dir_drop) for lfs_dir_drop
    lfs.o(i.lfs_removeattr) refers to lfs.o(i.lfs_commitattr) for lfs_commitattr
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_fs_forceconsistency) for lfs_fs_forceconsistency
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_dir_find) for lfs_dir_find
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_tag_id) for lfs_tag_id
    lfs.o(i.lfs_rename) refers to strlen.o(.text) for strlen
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_tag_type3) for lfs_tag_type3
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_dir_get) for lfs_dir_get
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_pair_fromle32) for lfs_pair_fromle32
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_dir_fetch) for lfs_dir_fetch
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_fs_preporphans) for lfs_fs_preporphans
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_pair_cmp) for lfs_pair_cmp
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_fs_prepmove) for lfs_fs_prepmove
    lfs.o(i.lfs_rename) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_dir_commit) for lfs_dir_commit
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_fs_pred) for lfs_fs_pred
    lfs.o(i.lfs_rename) refers to lfs.o(i.lfs_dir_drop) for lfs_dir_drop
    lfs.o(i.lfs_setattr) refers to lfs.o(i.lfs_commitattr) for lfs_commitattr
    lfs.o(i.lfs_stat) refers to lfs.o(i.lfs_dir_find) for lfs_dir_find
    lfs.o(i.lfs_stat) refers to lfs.o(i.lfs_tag_id) for lfs_tag_id
    lfs.o(i.lfs_stat) refers to lfs.o(i.lfs_dir_getinfo) for lfs_dir_getinfo
    lfs.o(i.lfs_superblock_fromle32) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_superblock_tole32) refers to lfs.o(i.lfs_tole32) for lfs_tole32
    lfs.o(i.lfs_tag_dsize) refers to lfs.o(i.lfs_tag_isdelete) for lfs_tag_isdelete
    lfs.o(i.lfs_tag_dsize) refers to lfs.o(i.lfs_tag_size) for lfs_tag_size
    lfs.o(i.lfs_tag_splice) refers to lfs.o(i.lfs_tag_chunk) for lfs_tag_chunk
    lfs.o(i.lfs_tobe32) refers to lfs.o(i.lfs_frombe32) for lfs_frombe32
    lfs.o(i.lfs_tole32) refers to lfs.o(i.lfs_fromle32) for lfs_fromle32
    lfs.o(i.lfs_unmount) refers to lfs.o(i.lfs_deinit) for lfs_deinit
    lfs.o(.constdata) refers to lfs.o(.conststring) for .conststring
    lfs_port.o(i.lfs_deskio_erase) refers to gd25qxx.o(i.spi_flash_sector_erase) for spi_flash_sector_erase
    lfs_port.o(i.lfs_deskio_prog) refers to gd25qxx.o(i.spi_flash_buffer_write) for spi_flash_buffer_write
    lfs_port.o(i.lfs_deskio_read) refers to gd25qxx.o(i.spi_flash_buffer_read) for spi_flash_buffer_read
    lfs_port.o(i.lfs_storage_init) refers to lfs_port.o(i.lfs_deskio_read) for lfs_deskio_read
    lfs_port.o(i.lfs_storage_init) refers to lfs_port.o(i.lfs_deskio_prog) for lfs_deskio_prog
    lfs_port.o(i.lfs_storage_init) refers to lfs_port.o(i.lfs_deskio_erase) for lfs_deskio_erase
    lfs_port.o(i.lfs_storage_init) refers to lfs_port.o(i.lfs_deskio_sync) for lfs_deskio_sync
    lfs_port.o(i.lfs_storage_init) refers to lfs_port.o(.bss) for lfs_read_buffer
    lfs_util.o(i.lfs_crc) refers to lfs_util.o(.constdata) for rtable
    oled.o(.rev16_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(.revsh_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(.rrx_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.I2C_Bus_Reset) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    oled.o(i.I2C_Bus_Reset) refers to perf_counter.o(i.delay_ms) for delay_ms
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_i2c.o(i.i2c_deinit) for i2c_deinit
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_i2c.o(i.i2c_clock_config) for i2c_clock_config
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_i2c.o(i.i2c_mode_addr_config) for i2c_mode_addr_config
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_i2c.o(i.i2c_enable) for i2c_enable
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_i2c.o(i.i2c_ack_config) for i2c_ack_config
    oled.o(i.OLED_Allfill) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.OLED_Allfill) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Allfill) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_Clear) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_Display_Off) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.OLED_Display_Off) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Display_On) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.OLED_Display_On) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Init) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.OLED_Init) refers to perf_counter.o(i.delay_ms) for delay_ms
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_Init) refers to oled.o(.data) for initcmd1
    oled.o(i.OLED_Pow) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.OLED_Set_Position) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.OLED_Set_Position) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_ShowChar) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for F8X16
    oled.o(i.OLED_ShowFloat) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.OLED_ShowFloat) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowHanzi) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.OLED_ShowHanzi) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowHanzi) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowHanzi) refers to oled.o(.constdata) for Hzk
    oled.o(i.OLED_ShowHzbig) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.OLED_ShowHzbig) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowHzbig) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowHzbig) refers to oled.o(.constdata) for Hzb
    oled.o(i.OLED_ShowNum) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowPic) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.OLED_ShowPic) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowPic) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowStr) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.OLED_ShowStr) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_Write_cmd) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_i2c.o(i.i2c_flag_get) for i2c_flag_get
    oled.o(i.OLED_Write_cmd) refers to oled.o(i.I2C_Bus_Reset) for I2C_Bus_Reset
    oled.o(i.OLED_Write_cmd) refers to perf_counter.o(i.delay_ms) for delay_ms
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_i2c.o(i.i2c_start_on_bus) for i2c_start_on_bus
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_i2c.o(i.i2c_master_addressing) for i2c_master_addressing
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_i2c.o(i.i2c_flag_clear) for i2c_flag_clear
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_dma.o(i.dma_memory_address_config) for dma_memory_address_config
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_dma.o(i.dma_transfer_number_config) for dma_transfer_number_config
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_i2c.o(i.i2c_dma_config) for i2c_dma_config
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_i2c.o(i.i2c_stop_on_bus) for i2c_stop_on_bus
    oled.o(i.OLED_Write_cmd) refers to mcu_cmic_gd32f470vet6.o(.data) for oled_cmd_buf
    oled.o(i.OLED_Write_data) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(i.OLED_Write_data) refers to gd32f4xx_i2c.o(i.i2c_flag_get) for i2c_flag_get
    oled.o(i.OLED_Write_data) refers to oled.o(i.I2C_Bus_Reset) for I2C_Bus_Reset
    oled.o(i.OLED_Write_data) refers to perf_counter.o(i.delay_ms) for delay_ms
    oled.o(i.OLED_Write_data) refers to gd32f4xx_i2c.o(i.i2c_start_on_bus) for i2c_start_on_bus
    oled.o(i.OLED_Write_data) refers to gd32f4xx_i2c.o(i.i2c_master_addressing) for i2c_master_addressing
    oled.o(i.OLED_Write_data) refers to gd32f4xx_i2c.o(i.i2c_flag_clear) for i2c_flag_clear
    oled.o(i.OLED_Write_data) refers to gd32f4xx_dma.o(i.dma_memory_address_config) for dma_memory_address_config
    oled.o(i.OLED_Write_data) refers to gd32f4xx_dma.o(i.dma_transfer_number_config) for dma_transfer_number_config
    oled.o(i.OLED_Write_data) refers to gd32f4xx_i2c.o(i.i2c_dma_config) for i2c_dma_config
    oled.o(i.OLED_Write_data) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    oled.o(i.OLED_Write_data) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    oled.o(i.OLED_Write_data) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    oled.o(i.OLED_Write_data) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    oled.o(i.OLED_Write_data) refers to gd32f4xx_i2c.o(i.i2c_stop_on_bus) for i2c_stop_on_bus
    oled.o(i.OLED_Write_data) refers to mcu_cmic_gd32f470vet6.o(.data) for oled_data_buf
    oled.o(.constdata) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled.o(.data) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    ebtn.o(i.bit_array_cmp) refers to memcmp.o(.text) for memcmp
    ebtn.o(i.ebtn_combo_btn_add_btn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_combo_btn_add_btn) refers to ebtn.o(i.ebtn_combo_btn_add_btn_by_idx) for ebtn_combo_btn_add_btn_by_idx
    ebtn.o(i.ebtn_combo_btn_remove_btn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_combo_btn_remove_btn) refers to ebtn.o(i.ebtn_combo_btn_remove_btn_by_idx) for ebtn_combo_btn_remove_btn_by_idx
    ebtn.o(i.ebtn_combo_register) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_get_btn_by_key_id) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_get_btn_index_by_btn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_get_btn_index_by_btn_dyn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_get_btn_index_by_key_id) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_get_config) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_get_current_state) refers to ebtn.o(i.bit_array_assign) for bit_array_assign
    ebtn.o(i.ebtn_get_current_state) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_get_total_btn_cnt) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    ebtn.o(i.ebtn_init) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_is_in_process) refers to ebtn.o(i.ebtn_is_btn_in_process) for ebtn_is_btn_in_process
    ebtn.o(i.ebtn_is_in_process) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_process) refers to ebtn.o(i.ebtn_get_current_state) for ebtn_get_current_state
    ebtn.o(i.ebtn_process) refers to ebtn.o(i.ebtn_process_with_curr_state) for ebtn_process_with_curr_state
    ebtn.o(i.ebtn_process_btn) refers to ebtn.o(i.bit_array_get) for bit_array_get
    ebtn.o(i.ebtn_process_btn) refers to ebtn.o(i.prv_process_btn) for prv_process_btn
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.bit_array_num_bits_set) for bit_array_num_bits_set
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.bit_array_and) for bit_array_and
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.bit_array_cmp) for bit_array_cmp
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.prv_process_btn) for prv_process_btn
    ebtn.o(i.ebtn_process_with_curr_state) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_and) for bit_array_and
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_cmp) for bit_array_cmp
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_or) for bit_array_or
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_get) for bit_array_get
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.ebtn_process_btn) for ebtn_process_btn
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.ebtn_process_btn_combo) for ebtn_process_btn_combo
    ebtn.o(i.ebtn_process_with_curr_state) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_register) refers to ebtn.o(i.ebtn_get_total_btn_cnt) for ebtn_get_total_btn_cnt
    ebtn.o(i.ebtn_register) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_set_combo_suppress_threshold) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_set_config) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.prv_get_combo_btn_by_key_id) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.prv_process_btn) refers to ebtn.o(i.ebtn_timer_sub) for ebtn_timer_sub
    ebtn.o(i.prv_process_btn) refers to ebtn.o(i.prv_get_combo_btn_by_key_id) for prv_get_combo_btn_by_key_id
    ebtn.o(i.prv_process_btn) refers to ebtn.o(i.bit_array_get) for bit_array_get
    ebtn.o(i.prv_process_btn) refers to ebtn.o(.bss) for ebtn_default
    sdio_sdcard.o(i.cmdsent_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.cmdsent_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_multi_data_mode_init) for dma_multi_data_mode_init
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_flow_controller_config) for dma_flow_controller_config
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_multi_data_mode_init) for dma_multi_data_mode_init
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_flow_controller_config) for dma_flow_controller_config
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    sdio_sdcard.o(i.gpio_config) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    sdio_sdcard.o(i.gpio_config) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    sdio_sdcard.o(i.gpio_config) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    sdio_sdcard.o(i.r1_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.r1_error_check) refers to gd32f4xx_sdio.o(i.sdio_command_index_get) for sdio_command_index_get
    sdio_sdcard.o(i.r1_error_check) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.r1_error_check) refers to sdio_sdcard.o(i.r1_error_type_check) for r1_error_type_check
    sdio_sdcard.o(i.r2_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.r3_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.r6_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.r6_error_check) refers to gd32f4xx_sdio.o(i.sdio_command_index_get) for sdio_command_index_get
    sdio_sdcard.o(i.r6_error_check) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.r7_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.rcu_config) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_block_read) refers to sdio_sdcard.o(i.sd_datablocksize_get) for sd_datablocksize_get
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_block_read) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_data_read) for sdio_data_read
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_interrupt_enable) for sdio_interrupt_enable
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_dma_enable) for sdio_dma_enable
    sdio_sdcard.o(i.sd_block_read) refers to sdio_sdcard.o(i.dma_receive_config) for dma_receive_config
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    sdio_sdcard.o(i.sd_block_read) refers to sdio_sdcard.o(.data) for transerror
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_block_write) refers to sdio_sdcard.o(i.sd_datablocksize_get) for sd_datablocksize_get
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_block_write) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_data_write) for sdio_data_write
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_interrupt_enable) for sdio_interrupt_enable
    sdio_sdcard.o(i.sd_block_write) refers to sdio_sdcard.o(i.dma_transfer_config) for dma_transfer_config
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_dma_enable) for sdio_dma_enable
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    sdio_sdcard.o(i.sd_block_write) refers to sdio_sdcard.o(i.sd_card_state_get) for sd_card_state_get
    sdio_sdcard.o(i.sd_block_write) refers to sdio_sdcard.o(.data) for transerror
    sdio_sdcard.o(i.sd_bus_mode_config) refers to sdio_sdcard.o(i.sd_bus_width_config) for sd_bus_width_config
    sdio_sdcard.o(i.sd_bus_mode_config) refers to gd32f4xx_sdio.o(i.sdio_clock_config) for sdio_clock_config
    sdio_sdcard.o(i.sd_bus_mode_config) refers to gd32f4xx_sdio.o(i.sdio_bus_mode_set) for sdio_bus_mode_set
    sdio_sdcard.o(i.sd_bus_mode_config) refers to gd32f4xx_sdio.o(i.sdio_hardware_clock_disable) for sdio_hardware_clock_disable
    sdio_sdcard.o(i.sd_bus_mode_config) refers to sdio_sdcard.o(.data) for cardtype
    sdio_sdcard.o(i.sd_bus_width_config) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_bus_width_config) refers to sdio_sdcard.o(i.sd_scr_get) for sd_scr_get
    sdio_sdcard.o(i.sd_bus_width_config) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_bus_width_config) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_bus_width_config) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_bus_width_config) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_bus_width_config) refers to sdio_sdcard.o(.data) for sd_scr
    sdio_sdcard.o(i.sd_card_capacity_get) refers to sdio_sdcard.o(.data) for cardtype
    sdio_sdcard.o(i.sd_card_capacity_get) refers to sdio_sdcard.o(.bss) for sd_csd
    sdio_sdcard.o(i.sd_card_information_get) refers to sdio_sdcard.o(.data) for cardtype
    sdio_sdcard.o(i.sd_card_information_get) refers to sdio_sdcard.o(.bss) for sd_cid
    sdio_sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_power_state_get) for sdio_power_state_get
    sdio_sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_card_init) refers to sdio_sdcard.o(i.r2_error_check) for r2_error_check
    sdio_sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_card_init) refers to sdio_sdcard.o(i.r6_error_check) for r6_error_check
    sdio_sdcard.o(i.sd_card_init) refers to sdio_sdcard.o(.data) for cardtype
    sdio_sdcard.o(i.sd_card_init) refers to sdio_sdcard.o(.bss) for sd_cid
    sdio_sdcard.o(i.sd_card_select_deselect) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_card_select_deselect) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_card_select_deselect) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_card_select_deselect) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_command_index_get) for sdio_command_index_get
    sdio_sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_card_state_get) refers to sdio_sdcard.o(i.r1_error_type_check) for r1_error_type_check
    sdio_sdcard.o(i.sd_card_state_get) refers to sdio_sdcard.o(.data) for sd_rca
    sdio_sdcard.o(i.sd_cardstatus_get) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_cardstatus_get) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_cardstatus_get) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_cardstatus_get) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_cardstatus_get) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_cardstatus_get) refers to sdio_sdcard.o(.data) for sd_rca
    sdio_sdcard.o(i.sd_erase) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_erase) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_erase) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_erase) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_erase) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_erase) refers to sdio_sdcard.o(i.sd_card_state_get) for sd_card_state_get
    sdio_sdcard.o(i.sd_erase) refers to sdio_sdcard.o(.bss) for sd_csd
    sdio_sdcard.o(i.sd_erase) refers to sdio_sdcard.o(.data) for cardtype
    sdio_sdcard.o(i.sd_init) refers to sdio_sdcard.o(i.rcu_config) for rcu_config
    sdio_sdcard.o(i.sd_init) refers to sdio_sdcard.o(i.gpio_config) for gpio_config
    sdio_sdcard.o(i.sd_init) refers to gd32f4xx_sdio.o(i.sdio_deinit) for sdio_deinit
    sdio_sdcard.o(i.sd_init) refers to sdio_sdcard.o(i.sd_power_on) for sd_power_on
    sdio_sdcard.o(i.sd_init) refers to sdio_sdcard.o(i.sd_card_init) for sd_card_init
    sdio_sdcard.o(i.sd_init) refers to gd32f4xx_sdio.o(i.sdio_clock_config) for sdio_clock_config
    sdio_sdcard.o(i.sd_init) refers to gd32f4xx_sdio.o(i.sdio_bus_mode_set) for sdio_bus_mode_set
    sdio_sdcard.o(i.sd_init) refers to gd32f4xx_sdio.o(i.sdio_hardware_clock_disable) for sdio_hardware_clock_disable
    sdio_sdcard.o(i.sd_interrupts_process) refers to gd32f4xx_sdio.o(i.sdio_interrupt_flag_get) for sdio_interrupt_flag_get
    sdio_sdcard.o(i.sd_interrupts_process) refers to sdio_sdcard.o(i.sd_transfer_stop) for sd_transfer_stop
    sdio_sdcard.o(i.sd_interrupts_process) refers to gd32f4xx_sdio.o(i.sdio_interrupt_flag_clear) for sdio_interrupt_flag_clear
    sdio_sdcard.o(i.sd_interrupts_process) refers to gd32f4xx_sdio.o(i.sdio_interrupt_disable) for sdio_interrupt_disable
    sdio_sdcard.o(i.sd_interrupts_process) refers to sdio_sdcard.o(.data) for transerror
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_lock_unlock) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_data_write) for sdio_data_write
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_lock_unlock) refers to sdio_sdcard.o(i.sd_card_state_get) for sd_card_state_get
    sdio_sdcard.o(i.sd_lock_unlock) refers to sdio_sdcard.o(.bss) for sd_csd
    sdio_sdcard.o(i.sd_lock_unlock) refers to sdio_sdcard.o(.data) for sd_rca
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_multiblocks_read) refers to sdio_sdcard.o(i.sd_datablocksize_get) for sd_datablocksize_get
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_multiblocks_read) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_data_read) for sdio_data_read
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_interrupt_enable) for sdio_interrupt_enable
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_dma_enable) for sdio_dma_enable
    sdio_sdcard.o(i.sd_multiblocks_read) refers to sdio_sdcard.o(i.dma_receive_config) for dma_receive_config
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    sdio_sdcard.o(i.sd_multiblocks_read) refers to sdio_sdcard.o(.data) for transerror
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_multiblocks_write) refers to sdio_sdcard.o(i.sd_datablocksize_get) for sd_datablocksize_get
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_multiblocks_write) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_data_write) for sdio_data_write
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_interrupt_enable) for sdio_interrupt_enable
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_dma_enable) for sdio_dma_enable
    sdio_sdcard.o(i.sd_multiblocks_write) refers to sdio_sdcard.o(i.dma_transfer_config) for dma_transfer_config
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    sdio_sdcard.o(i.sd_multiblocks_write) refers to sdio_sdcard.o(i.sd_card_state_get) for sd_card_state_get
    sdio_sdcard.o(i.sd_multiblocks_write) refers to sdio_sdcard.o(.data) for transerror
    sdio_sdcard.o(i.sd_power_off) refers to gd32f4xx_sdio.o(i.sdio_power_state_set) for sdio_power_state_set
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_clock_config) for sdio_clock_config
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_bus_mode_set) for sdio_bus_mode_set
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_hardware_clock_disable) for sdio_hardware_clock_disable
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_power_state_set) for sdio_power_state_set
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_clock_enable) for sdio_clock_enable
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_power_on) refers to sdio_sdcard.o(i.cmdsent_error_check) for cmdsent_error_check
    sdio_sdcard.o(i.sd_power_on) refers to sdio_sdcard.o(i.r7_error_check) for r7_error_check
    sdio_sdcard.o(i.sd_power_on) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_power_on) refers to sdio_sdcard.o(i.r3_error_check) for r3_error_check
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_power_on) refers to sdio_sdcard.o(.data) for cardtype
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_scr_get) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_data_read) for sdio_data_read
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_sdstatus_get) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_data_read) for sdio_data_read
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_sdstatus_get) refers to sdio_sdcard.o(.data) for sd_rca
    sdio_sdcard.o(i.sd_transfer_mode_config) refers to sdio_sdcard.o(.data) for transmode
    sdio_sdcard.o(i.sd_transfer_state_get) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_transfer_stop) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_transfer_stop) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_transfer_stop) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_transfer_stop) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    ff.o(i.check_fs) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.chk_mounted) refers to diskio.o(i.disk_status) for disk_status
    ff.o(i.chk_mounted) refers to diskio.o(i.disk_initialize) for disk_initialize
    ff.o(i.chk_mounted) refers to ff.o(i.check_fs) for check_fs
    ff.o(i.chk_mounted) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.chk_mounted) refers to ff.o(.data) for FatFs
    ff.o(i.create_chain) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.create_chain) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.create_name) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.create_name) refers to ff.o(i.chk_chr) for chk_chr
    ff.o(i.dir_find) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_find) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_find) refers to ff.o(i.mem_cmp) for mem_cmp
    ff.o(i.dir_find) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_next) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.dir_next) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.dir_next) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_next) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.dir_next) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.dir_read) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_read) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_register) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_register) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_register) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_register) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.dir_register) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.dir_remove) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_remove) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_sdi) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.dir_sdi) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_chmod) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_chmod) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_chmod) refers to ff.o(i.sync) for sync
    ff.o(i.f_close) refers to ff.o(i.f_sync) for f_sync
    ff.o(i.f_getfree) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_getfree) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_getfree) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_lseek) refers to ff.o(i.validate) for validate
    ff.o(i.f_lseek) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_lseek) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_lseek) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_lseek) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_lseek) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.f_mkdir) refers to diskio.o(i.get_fattime) for get_fattime
    ff.o(i.f_mkdir) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_mkdir) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_mkdir) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_mkdir) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_mkdir) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_mkdir) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.f_mkdir) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_mkdir) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_mkdir) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_mkdir) refers to ff.o(i.sync) for sync
    ff.o(i.f_mount) refers to ff.o(.data) for FatFs
    ff.o(i.f_open) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_open) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_open) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_open) refers to diskio.o(i.get_fattime) for get_fattime
    ff.o(i.f_open) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_open) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_opendir) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_opendir) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_opendir) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_read) refers to ff.o(i.validate) for validate
    ff.o(i.f_read) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_read) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_read) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.f_read) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_read) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_readdir) refers to ff.o(i.validate) for validate
    ff.o(i.f_readdir) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_readdir) refers to ff.o(i.dir_read) for dir_read
    ff.o(i.f_readdir) refers to ff.o(i.get_fileinfo) for get_fileinfo
    ff.o(i.f_readdir) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.f_rename) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_rename) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_rename) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_rename) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_rename) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_rename) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_rename) refers to ff.o(i.dir_remove) for dir_remove
    ff.o(i.f_rename) refers to ff.o(i.sync) for sync
    ff.o(i.f_stat) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_stat) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_stat) refers to ff.o(i.get_fileinfo) for get_fileinfo
    ff.o(i.f_sync) refers to ff.o(i.validate) for validate
    ff.o(i.f_sync) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_sync) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_sync) refers to diskio.o(i.get_fattime) for get_fattime
    ff.o(i.f_sync) refers to ff.o(i.sync) for sync
    ff.o(i.f_truncate) refers to ff.o(i.validate) for validate
    ff.o(i.f_truncate) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_truncate) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_truncate) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.f_unlink) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_unlink) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_unlink) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_unlink) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_unlink) refers to ff.o(i.dir_read) for dir_read
    ff.o(i.f_unlink) refers to ff.o(i.dir_remove) for dir_remove
    ff.o(i.f_unlink) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_unlink) refers to ff.o(i.sync) for sync
    ff.o(i.f_utime) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_utime) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_utime) refers to ff.o(i.sync) for sync
    ff.o(i.f_write) refers to ff.o(i.validate) for validate
    ff.o(i.f_write) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_write) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_write) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_write) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_write) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.follow_path) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.follow_path) refers to ff.o(i.create_name) for create_name
    ff.o(i.follow_path) refers to ff.o(i.dir_find) for dir_find
    ff.o(i.get_fat) refers to ff.o(i.move_window) for move_window
    ff.o(i.move_window) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.move_window) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.put_fat) refers to ff.o(i.move_window) for move_window
    ff.o(i.remove_chain) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.remove_chain) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.sync) refers to ff.o(i.move_window) for move_window
    ff.o(i.sync) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.sync) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.sync) refers to diskio.o(i.disk_ioctl) for disk_ioctl
    ff.o(i.validate) refers to diskio.o(i.disk_status) for disk_status
    diskio.o(i.disk_initialize) refers to sdio_sdcard.o(i.sd_init) for sd_init
    diskio.o(i.disk_initialize) refers to sdio_sdcard.o(i.sd_card_information_get) for sd_card_information_get
    diskio.o(i.disk_initialize) refers to sdio_sdcard.o(i.sd_card_select_deselect) for sd_card_select_deselect
    diskio.o(i.disk_initialize) refers to sdio_sdcard.o(i.sd_cardstatus_get) for sd_cardstatus_get
    diskio.o(i.disk_initialize) refers to sdio_sdcard.o(i.sd_bus_mode_config) for sd_bus_mode_config
    diskio.o(i.disk_initialize) refers to sdio_sdcard.o(i.sd_transfer_mode_config) for sd_transfer_mode_config
    diskio.o(i.disk_read) refers to sdio_sdcard.o(i.sd_block_read) for sd_block_read
    diskio.o(i.disk_read) refers to sdio_sdcard.o(i.sd_multiblocks_read) for sd_multiblocks_read
    diskio.o(i.disk_write) refers to sdio_sdcard.o(i.sd_block_write) for sd_block_write
    diskio.o(i.disk_write) refers to sdio_sdcard.o(i.sd_multiblocks_write) for sd_multiblocks_write
    btn_app.o(.rev16_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    btn_app.o(.revsh_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    btn_app.o(.rrx_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    btn_app.o(i.app_btn_init) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    btn_app.o(i.app_btn_init) refers to ebtn.o(i.ebtn_init) for ebtn_init
    btn_app.o(i.app_btn_init) refers to btn_app.o(i.prv_btn_event) for prv_btn_event
    btn_app.o(i.app_btn_init) refers to btn_app.o(i.prv_btn_get_state) for prv_btn_get_state
    btn_app.o(i.app_btn_init) refers to btn_app.o(.data) for btns
    btn_app.o(i.btn_task) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    btn_app.o(i.btn_task) refers to perf_counter.o(i.get_system_ms) for get_system_ms
    btn_app.o(i.btn_task) refers to ebtn.o(i.ebtn_process) for ebtn_process
    btn_app.o(i.prv_btn_event) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    btn_app.o(i.prv_btn_get_state) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    btn_app.o(i.prv_btn_get_state) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    btn_app.o(.constdata) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    btn_app.o(.data) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    btn_app.o(.data) refers to btn_app.o(.constdata) for defaul_ebtn_param
    led_app.o(.rev16_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    led_app.o(.revsh_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    led_app.o(.rrx_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    led_app.o(i.led_disp) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    led_app.o(i.led_disp) refers to led_app.o(.data) for temp_old
    led_app.o(i.led_task) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    led_app.o(i.led_task) refers to led_app.o(i.led_disp) for led_disp
    led_app.o(i.led_task) refers to led_app.o(.data) for ucLed
    led_app.o(.data) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled_app.o(.rev16_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled_app.o(.revsh_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled_app.o(.rrx_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled_app.o(i.oled_printf) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled_app.o(i.oled_printf) refers to vsnprintf.o(.text) for vsnprintf
    oled_app.o(i.oled_printf) refers to oled.o(i.OLED_ShowStr) for OLED_ShowStr
    oled_app.o(i.oled_task) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    oled_app.o(i.oled_task) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    oled_app.o(i.oled_task) refers to oled_app.o(i.oled_printf) for oled_printf
    oled_app.o(i.oled_task) refers to perf_counter.o(i.get_system_ms) for get_system_ms
    oled_app.o(i.oled_task) refers to mcu_cmic_gd32f470vet6.o(.data) for adc_value
    scheduler.o(.rev16_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    scheduler.o(.revsh_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    scheduler.o(.rrx_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    scheduler.o(i.scheduler_init) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    scheduler.o(i.scheduler_init) refers to scheduler.o(.data) for task_num
    scheduler.o(i.scheduler_run) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    scheduler.o(i.scheduler_run) refers to perf_counter.o(i.get_system_ms) for get_system_ms
    scheduler.o(i.scheduler_run) refers to scheduler.o(.data) for scheduler_task
    scheduler.o(.data) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    scheduler.o(.data) refers to led_app.o(i.led_task) for led_task
    scheduler.o(.data) refers to adc_app.o(i.adc_task) for adc_task
    scheduler.o(.data) refers to oled_app.o(i.oled_task) for oled_task
    scheduler.o(.data) refers to btn_app.o(i.btn_task) for btn_task
    scheduler.o(.data) refers to usart_app.o(i.uart_task) for uart_task
    scheduler.o(.data) refers to rtc_app.o(i.rtc_task) for rtc_task
    usart_app.o(.rev16_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    usart_app.o(.revsh_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    usart_app.o(.rrx_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    usart_app.o(i.my_printf) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    usart_app.o(i.my_printf) refers to vsnprintf.o(.text) for vsnprintf
    usart_app.o(i.my_printf) refers to gd32f4xx_usart.o(i.usart_data_transmit) for usart_data_transmit
    usart_app.o(i.my_printf) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    usart_app.o(i.my_printf) refers to usart_app.o(.data) for tx_count
    usart_app.o(i.uart_task) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    usart_app.o(i.uart_task) refers to usart_app.o(i.my_printf) for my_printf
    usart_app.o(i.uart_task) refers to rt_memclr.o(.text) for __aeabi_memclr
    usart_app.o(i.uart_task) refers to usart_app.o(.data) for rx_flag
    usart_app.o(i.uart_task) refers to usart_app.o(.bss) for uart_dma_buffer
    usart_app.o(.bss) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    usart_app.o(.data) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    sd_app.o(.rev16_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    sd_app.o(.revsh_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    sd_app.o(.rrx_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    sd_app.o(i.card_info_get) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    sd_app.o(i.card_info_get) refers to sdio_sdcard.o(i.sd_card_information_get) for sd_card_information_get
    sd_app.o(i.card_info_get) refers to usart_app.o(i.my_printf) for my_printf
    sd_app.o(i.card_info_get) refers to sdio_sdcard.o(i.sd_card_capacity_get) for sd_card_capacity_get
    sd_app.o(i.card_info_get) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    sd_app.o(i.memory_compare) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    sd_app.o(i.sd_fatfs_init) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    sd_app.o(i.sd_fatfs_init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    sd_app.o(i.sd_fatfs_test) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    sd_app.o(i.sd_fatfs_test) refers to diskio.o(i.disk_initialize) for disk_initialize
    sd_app.o(i.sd_fatfs_test) refers to sd_app.o(i.card_info_get) for card_info_get
    sd_app.o(i.sd_fatfs_test) refers to usart_app.o(i.my_printf) for my_printf
    sd_app.o(i.sd_fatfs_test) refers to ff.o(i.f_mount) for f_mount
    sd_app.o(i.sd_fatfs_test) refers to ff.o(i.f_open) for f_open
    sd_app.o(i.sd_fatfs_test) refers to __2sprintf.o(.text) for __2sprintf
    sd_app.o(i.sd_fatfs_test) refers to ff.o(i.f_write) for f_write
    sd_app.o(i.sd_fatfs_test) refers to ff.o(i.f_close) for f_close
    sd_app.o(i.sd_fatfs_test) refers to ff.o(i.f_read) for f_read
    sd_app.o(i.sd_fatfs_test) refers to sd_app.o(i.memory_compare) for memory_compare
    sd_app.o(i.sd_fatfs_test) refers to sd_app.o(.bss) for fs
    sd_app.o(i.sd_fatfs_test) refers to sd_app.o(.data) for result
    sd_app.o(.bss) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    sd_app.o(.data) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    rtc_app.o(.rev16_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    rtc_app.o(.revsh_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    rtc_app.o(.rrx_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    rtc_app.o(i.rtc_task) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    rtc_app.o(i.rtc_task) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    rtc_app.o(i.rtc_task) refers to oled_app.o(i.oled_printf) for oled_printf
    rtc_app.o(i.rtc_task) refers to mcu_cmic_gd32f470vet6.o(.bss) for rtc_initpara
    adc_app.o(.rev16_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    adc_app.o(.revsh_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    adc_app.o(.rrx_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    adc_app.o(i.adc_task) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    adc_app.o(i.adc_task) refers to mcu_cmic_gd32f470vet6.o(.data) for adc_value
    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_debug_freeze_disable) refers to gd32f4xx_dbg.o(i.dbg_periph_disable) for dbg_periph_disable
    gd32f4xx_can.o(i.can_debug_freeze_enable) refers to gd32f4xx_dbg.o(i.dbg_periph_enable) for dbg_periph_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_receive_message_length_get) for can_receive_message_length_get
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_error_get) for can_error_get
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_enet.o(i.enet_initpara_reset) for enet_initpara_reset
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_tx_disable) for enet_tx_disable
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_rx_disable) for enet_rx_disable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_tx_enable) for enet_tx_enable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_rx_enable) for enet_rx_enable
    gd32f4xx_enet.o(i.enet_frame_receive) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_frame_transmit) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_config) for enet_phy_config
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_default_init) for enet_default_init
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_initpara_config) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_initpara_reset) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_phyloopback_disable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phyloopback_enable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_ptpframe_receive_normal_mode) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_ptpframe_transmit_normal_mode) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_registers_get) refers to gd32f4xx_enet.o(.constdata) for enet_reg_tab
    gd32f4xx_enet.o(i.enet_rxframe_drop) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(i.enet_rxframe_drop) for enet_rxframe_drop
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_rxprocess_check_recovery) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_tx_disable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_enet.o(i.enet_tx_enable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_fmc.o(i.fmc_bank0_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_bank1_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_byte_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_halfword_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_mass_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_page_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_ready_wait) refers to gd32f4xx_fmc.o(i.fmc_state_get) for fmc_state_get
    gd32f4xx_fmc.o(i.fmc_sector_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_word_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_security_protection_config) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_user_write) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_i2c.o(i.i2c_clock_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_misc.o(i.nvic_irq_enable) refers to gd32f4xx_misc.o(i.nvic_priority_group_set) for nvic_priority_group_set
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_pmu.o(i.pmu_highdriver_switch_select) refers to gd32f4xx_pmu.o(i.pmu_flag_get) for pmu_flag_get
    gd32f4xx_pmu.o(i.pmu_to_deepsleepmode) refers to gd32f4xx_pmu.o(.bss) for reg_snap
    gd32f4xx_rcu.o(i.rcu_deinit) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_rcu.o(i.rcu_osci_stab_wait) refers to gd32f4xx_rcu.o(i.rcu_flag_get) for rcu_flag_get
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_second_adjust) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_i2s_clock_config) for rcu_i2s_clock_config
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_external_clock_mode0_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_clock_mode1_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_timer.o(i.timer_input_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_input_pwm_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_internal_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_usart.o(i.usart_baudrate_set) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    startup_gd32f450_470.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_gd32f450_470.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_gd32f450_470.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(.text) for Reset_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_gd32f450_470.o(RESET) refers to systick_wrapper_ual.o(.text) for SysTick_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.USART0_IRQHandler) for USART0_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.SDIO_IRQHandler) for SDIO_IRQHandler
    startup_gd32f450_470.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_gd32f450_470.o(.text) refers to system_gd32f4xx.o(i.SystemInit) for SystemInit
    startup_gd32f450_470.o(.text) refers to __main.o(!!!main) for __main
    startup_gd32f450_470.o(.text) refers to startup_gd32f450_470.o(HEAP) for Heap_Mem
    startup_gd32f450_470.o(.text) refers to startup_gd32f450_470.o(STACK) for Stack_Mem
    system_gd32f4xx.o(i.SystemCoreClockUpdate) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    system_gd32f4xx.o(i.SystemInit) refers to system_gd32f4xx.o(i._soft_delay_) for _soft_delay_
    system_gd32f4xx.o(i.SystemInit) refers to system_gd32f4xx.o(i.system_clock_config) for system_clock_config
    system_gd32f4xx.o(i.system_clock_config) refers to system_gd32f4xx.o(i.system_clock_240m_25m_hxtal) for system_clock_240m_25m_hxtal
    perf_counter.o(.rev16_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(.revsh_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(.rrx_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.EventRecorderTimerGetCount) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.EventRecorderTimerGetCount) refers to perf_counter.o(i.get_system_ticks) for get_system_ticks
    perf_counter.o(i.EventRecorderTimerGetFreq) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.EventRecorderTimerGetFreq) refers to perfc_port_default.o(i.perfc_port_get_system_timer_freq) for perfc_port_get_system_timer_freq
    perf_counter.o(i.EventRecorderTimerSetup) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.__on_context_switch_in) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.__on_context_switch_in) refers to perf_counter.o(i.get_system_ticks) for get_system_ticks
    perf_counter.o(i.__on_context_switch_out) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.__on_context_switch_out) refers to perf_counter.o(i.get_system_ticks) for get_system_ticks
    perf_counter.o(i.__on_context_switch_out) refers to perf_counter.o(.data) for g_nOffset
    perf_counter.o(i.__perf_counter_init) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.__perf_counter_init) refers to perf_counter.o(i.init_cycle_counter) for init_cycle_counter
    perf_counter.o(i.__perf_os_patch_init) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.__perfc_is_time_out) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.__perfc_is_time_out) refers to perf_counter.o(i.get_system_ticks) for get_system_ticks
    perf_counter.o(i.__start_task_cycle_counter) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.__start_task_cycle_counter) refers to perf_counter.o(i.get_rtos_task_cycle_info) for get_rtos_task_cycle_info
    perf_counter.o(i.__start_task_cycle_counter) refers to perf_counter.o(i.perfc_port_disable_global_interrupt) for perfc_port_disable_global_interrupt
    perf_counter.o(i.__start_task_cycle_counter) refers to perf_counter.o(i.get_system_ticks) for get_system_ticks
    perf_counter.o(i.__start_task_cycle_counter) refers to perf_counter.o(i.perfc_port_resume_global_interrupt) for perfc_port_resume_global_interrupt
    perf_counter.o(i.__stop_task_cycle_counter) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.__stop_task_cycle_counter) refers to perf_counter.o(i.get_rtos_task_cycle_info) for get_rtos_task_cycle_info
    perf_counter.o(i.__stop_task_cycle_counter) refers to perf_counter.o(i.perfc_port_disable_global_interrupt) for perfc_port_disable_global_interrupt
    perf_counter.o(i.__stop_task_cycle_counter) refers to perf_counter.o(i.get_system_ticks) for get_system_ticks
    perf_counter.o(i.__stop_task_cycle_counter) refers to perf_counter.o(i.perfc_port_resume_global_interrupt) for perfc_port_resume_global_interrupt
    perf_counter.o(i.__stop_task_cycle_counter) refers to perf_counter.o(.data) for g_nOffset
    perf_counter.o(i.before_cycle_counter_reconfiguration) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.before_cycle_counter_reconfiguration) refers to perf_counter.o(i.perfc_port_disable_global_interrupt) for perfc_port_disable_global_interrupt
    perf_counter.o(i.before_cycle_counter_reconfiguration) refers to perfc_port_default.o(i.perfc_port_stop_system_timer_counting) for perfc_port_stop_system_timer_counting
    perf_counter.o(i.before_cycle_counter_reconfiguration) refers to perfc_port_default.o(i.perfc_port_is_system_timer_ovf_pending) for perfc_port_is_system_timer_ovf_pending
    perf_counter.o(i.before_cycle_counter_reconfiguration) refers to perfc_port_default.o(i.perfc_port_clear_system_timer_ovf_pending) for perfc_port_clear_system_timer_ovf_pending
    perf_counter.o(i.before_cycle_counter_reconfiguration) refers to perf_counter.o(i.perfc_port_insert_to_system_timer_insert_ovf_handler) for perfc_port_insert_to_system_timer_insert_ovf_handler
    perf_counter.o(i.before_cycle_counter_reconfiguration) refers to perf_counter.o(i.get_system_ticks) for get_system_ticks
    perf_counter.o(i.before_cycle_counter_reconfiguration) refers to perfc_port_default.o(i.perfc_port_clear_system_timer_counter) for perfc_port_clear_system_timer_counter
    perf_counter.o(i.before_cycle_counter_reconfiguration) refers to perf_counter.o(i.perfc_port_resume_global_interrupt) for perfc_port_resume_global_interrupt
    perf_counter.o(i.before_cycle_counter_reconfiguration) refers to perf_counter.o(.data) for s_lSystemClockCounts
    perf_counter.o(i.check_systick) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.check_systick) refers to perfc_port_default.o(i.perfc_port_get_system_timer_elapsed) for perfc_port_get_system_timer_elapsed
    perf_counter.o(i.check_systick) refers to perfc_port_default.o(i.perfc_port_is_system_timer_ovf_pending) for perfc_port_is_system_timer_ovf_pending
    perf_counter.o(i.check_systick) refers to perfc_port_default.o(i.perfc_port_get_system_timer_top) for perfc_port_get_system_timer_top
    perf_counter.o(i.clock) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.clock) refers to perf_counter.o(i.get_system_ticks) for get_system_ticks
    perf_counter.o(i.delay_ms) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.delay_ms) refers to perf_counter.o(i.get_system_ticks) for get_system_ticks
    perf_counter.o(i.delay_ms) refers to perf_counter.o(.data) for s_wMSUnit
    perf_counter.o(i.delay_us) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.delay_us) refers to perf_counter.o(i.get_system_ticks) for get_system_ticks
    perf_counter.o(i.delay_us) refers to perf_counter.o(.data) for s_wUSUnit
    perf_counter.o(i.disable_task_cycle_info) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.disable_task_cycle_info) refers to perf_counter.o(i.perfc_port_disable_global_interrupt) for perfc_port_disable_global_interrupt
    perf_counter.o(i.disable_task_cycle_info) refers to perf_counter.o(i.perfc_port_resume_global_interrupt) for perfc_port_resume_global_interrupt
    perf_counter.o(i.enable_task_cycle_info) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.enable_task_cycle_info) refers to perf_counter.o(i.perfc_port_disable_global_interrupt) for perfc_port_disable_global_interrupt
    perf_counter.o(i.enable_task_cycle_info) refers to perf_counter.o(i.perfc_port_resume_global_interrupt) for perfc_port_resume_global_interrupt
    perf_counter.o(i.get_rtos_task_cycle_info) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.get_system_ms) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.get_system_ms) refers to perf_counter.o(i.perfc_port_disable_global_interrupt) for perfc_port_disable_global_interrupt
    perf_counter.o(i.get_system_ms) refers to perf_counter.o(i.check_systick) for check_systick
    perf_counter.o(i.get_system_ms) refers to llsdiv.o(.text) for __aeabi_ldivmod
    perf_counter.o(i.get_system_ms) refers to perf_counter.o(i.perfc_port_resume_global_interrupt) for perfc_port_resume_global_interrupt
    perf_counter.o(i.get_system_ms) refers to perf_counter.o(.data) for s_wMSResidule
    perf_counter.o(i.get_system_ticks) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.get_system_ticks) refers to perf_counter.o(i.perfc_port_disable_global_interrupt) for perfc_port_disable_global_interrupt
    perf_counter.o(i.get_system_ticks) refers to perf_counter.o(i.check_systick) for check_systick
    perf_counter.o(i.get_system_ticks) refers to perf_counter.o(i.perfc_port_resume_global_interrupt) for perfc_port_resume_global_interrupt
    perf_counter.o(i.get_system_ticks) refers to perf_counter.o(.data) for s_lSystemClockCounts
    perf_counter.o(i.get_system_us) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.get_system_us) refers to perf_counter.o(i.perfc_port_disable_global_interrupt) for perfc_port_disable_global_interrupt
    perf_counter.o(i.get_system_us) refers to perf_counter.o(i.check_systick) for check_systick
    perf_counter.o(i.get_system_us) refers to llsdiv.o(.text) for __aeabi_ldivmod
    perf_counter.o(i.get_system_us) refers to perf_counter.o(i.perfc_port_resume_global_interrupt) for perfc_port_resume_global_interrupt
    perf_counter.o(i.get_system_us) refers to perf_counter.o(.data) for s_wUSResidule
    perf_counter.o(i.init_cycle_counter) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.init_cycle_counter) refers to perf_counter.o(i.perfc_port_disable_global_interrupt) for perfc_port_disable_global_interrupt
    perf_counter.o(i.init_cycle_counter) refers to perfc_port_default.o(i.perfc_port_init_system_timer) for perfc_port_init_system_timer
    perf_counter.o(i.init_cycle_counter) refers to perfc_port_default.o(i.perfc_port_clear_system_timer_ovf_pending) for perfc_port_clear_system_timer_ovf_pending
    perf_counter.o(i.init_cycle_counter) refers to perf_counter.o(i.perfc_port_resume_global_interrupt) for perfc_port_resume_global_interrupt
    perf_counter.o(i.init_cycle_counter) refers to perf_counter.o(i.update_perf_counter) for update_perf_counter
    perf_counter.o(i.init_cycle_counter) refers to perf_counter.o(i.__perf_os_patch_init) for __perf_os_patch_init
    perf_counter.o(i.init_cycle_counter) refers to perf_counter.o(.data) for s_lSystemClockCounts
    perf_counter.o(i.init_task_cycle_counter) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.init_task_cycle_counter) refers to perf_counter.o(i.get_rtos_task_cycle_info) for get_rtos_task_cycle_info
    perf_counter.o(i.init_task_cycle_counter) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    perf_counter.o(i.init_task_cycle_counter) refers to perf_counter.o(i.get_system_ticks) for get_system_ticks
    perf_counter.o(i.init_task_cycle_info) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.init_task_cycle_info) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    perf_counter.o(i.perfc_check_task_stack_canary_safe) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.perfc_check_task_stack_canary_safe) refers to perf_counter.o(i.get_rtos_task_cycle_info) for get_rtos_task_cycle_info
    perf_counter.o(i.perfc_convert_ms_to_ticks) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.perfc_convert_ms_to_ticks) refers to perf_counter.o(.data) for s_wMSUnit
    perf_counter.o(i.perfc_convert_ticks_to_ms) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.perfc_convert_ticks_to_ms) refers to llsdiv.o(.text) for __aeabi_ldivmod
    perf_counter.o(i.perfc_convert_ticks_to_ms) refers to perf_counter.o(.data) for s_wMSUnit
    perf_counter.o(i.perfc_convert_ticks_to_us) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.perfc_convert_ticks_to_us) refers to llsdiv.o(.text) for __aeabi_ldivmod
    perf_counter.o(i.perfc_convert_ticks_to_us) refers to perf_counter.o(.data) for s_wUSUnit
    perf_counter.o(i.perfc_convert_us_to_ticks) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.perfc_convert_us_to_ticks) refers to perf_counter.o(.data) for s_wUSUnit
    perf_counter.o(i.perfc_get_systimer_frequency) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.perfc_get_systimer_frequency) refers to perfc_port_default.o(i.perfc_port_get_system_timer_freq) for perfc_port_get_system_timer_freq
    perf_counter.o(i.perfc_port_disable_global_interrupt) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.perfc_port_insert_to_system_timer_insert_ovf_handler) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.perfc_port_insert_to_system_timer_insert_ovf_handler) refers to perfc_port_default.o(i.perfc_port_get_system_timer_top) for perfc_port_get_system_timer_top
    perf_counter.o(i.perfc_port_insert_to_system_timer_insert_ovf_handler) refers to perf_counter.o(i.perfc_port_disable_global_interrupt) for perfc_port_disable_global_interrupt
    perf_counter.o(i.perfc_port_insert_to_system_timer_insert_ovf_handler) refers to llsdiv.o(.text) for __aeabi_ldivmod
    perf_counter.o(i.perfc_port_insert_to_system_timer_insert_ovf_handler) refers to perf_counter.o(i.perfc_port_resume_global_interrupt) for perfc_port_resume_global_interrupt
    perf_counter.o(i.perfc_port_insert_to_system_timer_insert_ovf_handler) refers to perf_counter.o(.data) for s_lSystemClockCounts
    perf_counter.o(i.perfc_port_resume_global_interrupt) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.register_task_cycle_agent) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.register_task_cycle_agent) refers to perf_counter.o(i.perfc_port_disable_global_interrupt) for perfc_port_disable_global_interrupt
    perf_counter.o(i.register_task_cycle_agent) refers to perf_counter.o(i.get_rtos_task_cycle_info) for get_rtos_task_cycle_info
    perf_counter.o(i.register_task_cycle_agent) refers to perf_counter.o(i.perfc_port_resume_global_interrupt) for perfc_port_resume_global_interrupt
    perf_counter.o(i.resume_task_cycle_info) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.unregister_task_cycle_agent) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.unregister_task_cycle_agent) refers to perf_counter.o(i.perfc_port_disable_global_interrupt) for perfc_port_disable_global_interrupt
    perf_counter.o(i.unregister_task_cycle_agent) refers to perf_counter.o(i.perfc_port_resume_global_interrupt) for perfc_port_resume_global_interrupt
    perf_counter.o(i.update_perf_counter) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(i.update_perf_counter) refers to perfc_port_default.o(i.perfc_port_get_system_timer_freq) for perfc_port_get_system_timer_freq
    perf_counter.o(i.update_perf_counter) refers to perf_counter.o(i.perfc_port_disable_global_interrupt) for perfc_port_disable_global_interrupt
    perf_counter.o(i.update_perf_counter) refers to perf_counter.o(i.get_system_ticks) for get_system_ticks
    perf_counter.o(i.update_perf_counter) refers to perf_counter.o(i.perfc_port_resume_global_interrupt) for perfc_port_resume_global_interrupt
    perf_counter.o(i.update_perf_counter) refers to perf_counter.o(.data) for s_wUSUnit
    perf_counter.o(.data) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(.init_array) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perf_counter.o(.init_array) refers to perf_counter.o(i.__perf_counter_init) for __perf_counter_init
    perf_counter.o(.init_array) refers to init_aeabi.o(.text) for __cpp_initialize__aeabi_
    perfc_port_default.o(.rev16_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perfc_port_default.o(.revsh_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perfc_port_default.o(.rrx_text) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perfc_port_default.o(i.perfc_port_clear_system_timer_counter) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perfc_port_default.o(i.perfc_port_clear_system_timer_ovf_pending) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perfc_port_default.o(i.perfc_port_get_system_timer_elapsed) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perfc_port_default.o(i.perfc_port_get_system_timer_freq) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perfc_port_default.o(i.perfc_port_get_system_timer_freq) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    perfc_port_default.o(i.perfc_port_get_system_timer_top) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perfc_port_default.o(i.perfc_port_init_system_timer) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perfc_port_default.o(i.perfc_port_is_system_timer_ovf_pending) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    perfc_port_default.o(i.perfc_port_stop_system_timer_counting) refers (Special) to systick_wrapper_ual.o(.text) for __ensure_systick_wrapper
    systick_wrapper_ual.o(.text) refers to perf_counter.o(i.perfc_port_insert_to_system_timer_insert_ovf_handler) for perfc_port_insert_to_system_timer_insert_ovf_handler
    systick_wrapper_ual.o(.text) refers to gd32f4xx_it.o(i.SysTick_Handler) for $Super$$SysTick_Handler
    malloc.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    malloc.o(.text) refers (Special) to init_alloc.o(.text) for _init_alloc
    malloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    malloc.o(.text) refers to heapstubs.o(.text) for __Heap_Alloc
    free.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    free.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    free.o(.text) refers to heapstubs.o(.text) for __Heap_Free
    h1_alloc.o(.text) refers (Special) to h1_init.o(.text) for __Heap_Initialize
    h1_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_alloc.o(.text) refers to init_alloc.o(.text) for __Heap_Full
    h1_free.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_alloc_mt.o(.text) refers (Special) to h1_init.o(.text) for __Heap_Initialize
    h1_alloc_mt.o(.text) refers to init_alloc.o(.text) for __Heap_Full
    h1_alloc_mt.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_free_mt.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._FDIterate) refers to heap2.o(.conststring) for .conststring
    heap2.o(i.___Heap_ProvideMemory$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i.___Heap_ProvideMemory$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i.___Heap_Stats$realtime) refers to heap2.o(i._Heap2_StatsIterate) for _Heap2_StatsIterate
    heap2.o(i.___Heap_Valid$realtime) refers to heap2.o(i._FDIterate) for _FDIterate
    heap2.o(i.___Heap_Valid$realtime) refers to heap2.o(.conststring) for .conststring
    heap2.o(i._free$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._free$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i._free$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._malloc$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2.o(i._malloc$realtime) refers to init_alloc.o(.text) for __Heap_Full
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._posix_memalign$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2.o(i._posix_memalign$realtime) refers to init_alloc.o(.text) for __Heap_Full
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._realloc$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._realloc$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._realloc$realtime) refers to h1_free.o(.text) for free
    heap2.o(i._realloc$realtime) refers to h1_alloc.o(.text) for malloc
    heap2.o(i._realloc$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i._realloc$realtime) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    heap2mt.o(i._FDIterate) refers to heap2mt.o(.conststring) for .conststring
    heap2mt.o(i.___Heap_Initialize$realtime$concurrent) refers to mutex_dummy.o(.text) for _mutex_initialize
    heap2mt.o(i.___Heap_ProvideMemory$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i.___Heap_ProvideMemory$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i.___Heap_Stats$realtime$concurrent) refers to heap2mt.o(i._Heap2_StatsIterate) for _Heap2_StatsIterate
    heap2mt.o(i.___Heap_Valid$realtime$concurrent) refers to heap2mt.o(i._FDIterate) for _FDIterate
    heap2mt.o(i.___Heap_Valid$realtime$concurrent) refers to heap2mt.o(.conststring) for .conststring
    heap2mt.o(i._free$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._free$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i._free$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._malloc$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2mt.o(i._malloc$realtime$concurrent) refers to init_alloc.o(.text) for __Heap_Full
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to init_alloc.o(.text) for __Heap_Full
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._realloc$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._realloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._realloc$realtime$concurrent) refers to h1_free.o(.text) for free
    heap2mt.o(i._realloc$realtime$concurrent) refers to h1_alloc.o(.text) for malloc
    heap2mt.o(i._realloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i._realloc$realtime$concurrent) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    llsdiv.o(.text) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    vsnprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsnprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsnprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsnprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsnprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsnprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsnprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsnprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsnprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsnprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsnprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsnprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsnprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsnprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsnprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsnprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsnprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsnprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsnprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsnprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsnprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsnprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsnprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsnprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsnprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsnprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsnprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsnprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsnprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsnprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsnprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsnprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsnprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsnprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsnprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsnprintf.o(.text) refers to _sputc.o(.text) for _sputc
    vsnprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to stdio_streams.o(.bss) for __stdout
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to stdio_streams.o(.bss) for __stdout
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    assert.o(.text) refers to assert_puts.o(.text) for __assert_puts
    assert.o(.text) refers to abort.o(.text) for abort
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    aeabi_memset.o(.text) refers to rt_memclr.o(.text) for _memset
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    init_aeabi.o(.emb_text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000031) for __rt_lib_init_cpp_2
    init_aeabi.o(.init_array) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000031) for __rt_lib_init_cpp_2
    init_aeabi.o(.init_array) refers to init_aeabi.o(.text) for __cpp_initialize__aeabi_
    init_aeabi.o(.dummy_text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000031) for __rt_lib_init_cpp_2
    init_aeabi.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000031) for __rt_lib_init_cpp_2
    init_aeabi.o(.ARM.exidx) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000031) for __rt_lib_init_cpp_2
    init_aeabi.o(.ARM.exidx) refers to init_aeabi.o(.text) for .text
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    rt_heap_descriptor.o(.text) refers to rt_heap_descriptor.o(.bss) for __rt_heap_descriptor_data
    rt_heap_descriptor_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    init_alloc.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    init_alloc.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000005) for __rt_lib_init_heap_2
    init_alloc.o(.text) refers (Special) to maybetermalloc1.o(.emb_text) for _maybe_terminate_alloc
    init_alloc.o(.text) refers to h1_extend.o(.text) for __Heap_ProvideMemory
    init_alloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    init_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    init_alloc.o(.text) refers to h1_init.o(.text) for __Heap_Initialize
    h1_init_mt.o(.text) refers to mutex_dummy.o(.text) for _mutex_initialize
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to main.o(i.fputc) for fputc
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    stdio_streams.o(.bss) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.bss) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.bss) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.data) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.data) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.data) refers (Special) to initio.o(.text) for _initio
    abort.o(.text) refers to defsig_abrt_outer.o(.text) for __rt_SIGABRT
    abort.o(.text) refers (Weak) to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    abort.o(.text) refers to sys_exit.o(.text) for _sys_exit
    assert_puts.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    _get_argv.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv.o(.text) refers to h1_alloc.o(.text) for malloc
    _get_argv.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000005) refers (Weak) to init_alloc.o(.text) for _init_alloc
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000024) refers (Weak) to initio.o(.text) for _initio
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000031) refers (Weak) to init_aeabi.o(.text) for __cpp_initialize__aeabi_
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    maybetermalloc2.o(.emb_text) refers (Special) to term_alloc.o(.text) for _terminate_alloc
    h1_extend.o(.text) refers to h1_free.o(.text) for free
    h1_extend_mt.o(.text) refers to h1_free_mt.o(.text) for _free_internal
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    initio.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000024) for __rt_lib_init_stdio_2
    initio.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000005) for __rt_lib_shutdown_stdio_2
    initio.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    initio.o(.text) refers to fopen.o(.text) for freopen
    initio.o(.text) refers to defsig_rtred_outer.o(.text) for __rt_SIGRTRED
    initio.o(.text) refers to setvbuf.o(.text) for setvbuf
    initio.o(.text) refers to fclose.o(.text) for _fclose_internal
    initio.o(.text) refers to h1_free.o(.text) for free
    initio.o(.text) refers to stdio_streams.o(.bss) for __stdin
    initio.o(.text) refers to stdio_streams.o(.bss) for __stdout
    initio.o(.text) refers to stdio_streams.o(.bss) for __stderr
    initio.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdin
    initio.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdout
    initio.o(.text) refers to stdio_streams.o(.data) for __aeabi_stderr
    initio.o(.text) refers to sys_io.o(.constdata) for __stdin_name
    initio.o(.text) refers to sys_io.o(.constdata) for __stdout_name
    initio.o(.text) refers to sys_io.o(.constdata) for __stderr_name
    initio_locked.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000024) for __rt_lib_init_stdio_2
    initio_locked.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000005) for __rt_lib_shutdown_stdio_2
    initio_locked.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    initio_locked.o(.text) refers to fopen.o(.text) for freopen
    initio_locked.o(.text) refers to defsig_rtred_outer.o(.text) for __rt_SIGRTRED
    initio_locked.o(.text) refers to setvbuf.o(.text) for setvbuf
    initio_locked.o(.text) refers to fclose.o(.text) for _fclose_internal
    initio_locked.o(.text) refers to h1_free.o(.text) for free
    initio_locked.o(.text) refers to stdio_streams.o(.bss) for __stdin
    initio_locked.o(.text) refers to stdio_streams.o(.bss) for __stdout
    initio_locked.o(.text) refers to stdio_streams.o(.bss) for __stderr
    initio_locked.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdin
    initio_locked.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdout
    initio_locked.o(.text) refers to stdio_streams.o(.data) for __aeabi_stderr
    initio_locked.o(.text) refers to streamlock.o(.data) for _stream_list_lock
    initio_locked.o(.text) refers to sys_io.o(.constdata) for __stdin_name
    initio_locked.o(.text) refers to sys_io.o(.constdata) for __stdout_name
    initio_locked.o(.text) refers to sys_io.o(.constdata) for __stderr_name
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    defsig_abrt_outer.o(.text) refers to defsig_abrt_inner.o(.text) for __rt_SIGABRT_inner
    defsig_abrt_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_abrt_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    heapauxa.o(.text) refers to heapauxa.o(.data) for .data
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_io.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_io.o(.text) refers to strlen.o(.text) for strlen
    sys_io.o(.constdata) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.constdata) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_io.o(.constdata) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.constdata) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_io.o(.constdata) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.constdata) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_gd32f450_470.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    term_alloc.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_heap_2
    term_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    term_alloc.o(.text) refers to h1_final.o(.text) for __Heap_Finalize
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    streamlock.o(.data) refers (Special) to initio.o(.text) for _initio
    fopen.o(.text) refers to fclose.o(.text) for _fclose_internal
    fopen.o(.text) refers to sys_io.o(.text) for _sys_open
    fopen.o(.text) refers to fseek.o(.text) for _fseek
    fopen.o(.text) refers to h1_alloc.o(.text) for malloc
    fopen.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fopen.o(.text) refers to stdio_streams.o(.bss) for __stdin
    fclose.o(.text) refers to stdio.o(.text) for _fflush
    fclose.o(.text) refers to sys_io.o(.text) for _sys_close
    fclose.o(.text) refers to h1_free.o(.text) for free
    fclose.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fopen_locked.o(.text) refers to fclose.o(.text) for _fclose_internal
    fopen_locked.o(.text) refers to sys_io.o(.text) for _sys_open
    fopen_locked.o(.text) refers to fseek.o(.text) for _fseek
    fopen_locked.o(.text) refers to h1_alloc.o(.text) for malloc
    fopen_locked.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fopen_locked.o(.text) refers to streamlock.o(.data) for _stream_list_lock
    fopen_locked.o(.text) refers to stdio_streams.o(.bss) for __stdin
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_outer.o(.text) refers to defsig_rtred_inner.o(.text) for __rt_SIGRTRED_inner
    defsig_rtred_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtred_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libshutdown2.o(.ARM.Collect$$libshutdown$$00000005) refers (Weak) to initio.o(.text) for _terminateio
    libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) refers (Weak) to term_alloc.o(.text) for _terminate_alloc
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    fseek.o(.text) refers to sys_io.o(.text) for _sys_istty
    fseek.o(.text) refers to ftell.o(.text) for _ftell_internal
    fseek.o(.text) refers to stdio.o(.text) for _seterr
    stdio.o(.text) refers to sys_io.o(.text) for _sys_seek
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    ftell.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig.o(CL$$defsig) refers to defsig_abrt_inner.o(.text) for __rt_SIGABRT_inner
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtred_inner.o(.text) for __rt_SIGRTRED_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1


==============================================================================

Removing Unused input sections from the image.

    Removing gd32f4xx_it.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_it.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_it.o(.rrx_text), (6 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(i.fputc), (36 bytes).
    Removing systick.o(.rev16_text), (4 bytes).
    Removing systick.o(.revsh_text), (4 bytes).
    Removing systick.o(.rrx_text), (6 bytes).
    Removing mcu_cmic_gd32f470vet6.o(.rev16_text), (4 bytes).
    Removing mcu_cmic_gd32f470vet6.o(.revsh_text), (4 bytes).
    Removing mcu_cmic_gd32f470vet6.o(.rrx_text), (6 bytes).
    Removing gd25qxx.o(.rev16_text), (4 bytes).
    Removing gd25qxx.o(.revsh_text), (4 bytes).
    Removing gd25qxx.o(.rrx_text), (6 bytes).
    Removing gd25qxx.o(i.spi_flash_buffer_write), (290 bytes).
    Removing gd25qxx.o(i.spi_flash_bulk_erase), (44 bytes).
    Removing gd25qxx.o(i.spi_flash_page_write), (92 bytes).
    Removing gd25qxx.o(i.spi_flash_sector_erase), (68 bytes).
    Removing gd25qxx.o(i.spi_flash_send_halfword_dma), (268 bytes).
    Removing gd25qxx.o(i.spi_flash_start_read_sequence), (48 bytes).
    Removing gd25qxx.o(i.spi_flash_transmit_receive_dma), (292 bytes).
    Removing gd25qxx.o(i.spi_flash_wait_for_dma_end), (44 bytes).
    Removing gd25qxx.o(i.spi_flash_wait_for_write_end), (56 bytes).
    Removing gd25qxx.o(i.spi_flash_write_enable), (36 bytes).
    Removing lfs.o(i.lfs_aligndown), (14 bytes).
    Removing lfs.o(i.lfs_alignup), (18 bytes).
    Removing lfs.o(i.lfs_alloc), (268 bytes).
    Removing lfs.o(i.lfs_alloc_ack), (8 bytes).
    Removing lfs.o(i.lfs_alloc_lookahead), (66 bytes).
    Removing lfs.o(i.lfs_bd_cmp), (102 bytes).
    Removing lfs.o(i.lfs_bd_erase), (128 bytes).
    Removing lfs.o(i.lfs_bd_flush), (268 bytes).
    Removing lfs.o(i.lfs_bd_prog), (364 bytes).
    Removing lfs.o(i.lfs_bd_read), (476 bytes).
    Removing lfs.o(i.lfs_bd_sync), (112 bytes).
    Removing lfs.o(i.lfs_cache_drop), (8 bytes).
    Removing lfs.o(i.lfs_cache_zero), (26 bytes).
    Removing lfs.o(i.lfs_commitattr), (120 bytes).
    Removing lfs.o(i.lfs_ctz), (18 bytes).
    Removing lfs.o(i.lfs_ctz_extend), (600 bytes).
    Removing lfs.o(i.lfs_ctz_find), (272 bytes).
    Removing lfs.o(i.lfs_ctz_fromle32), (22 bytes).
    Removing lfs.o(i.lfs_ctz_index), (72 bytes).
    Removing lfs.o(i.lfs_ctz_tole32), (22 bytes).
    Removing lfs.o(i.lfs_ctz_traverse), (170 bytes).
    Removing lfs.o(i.lfs_deinit), (44 bytes).
    Removing lfs.o(i.lfs_dir_alloc), (152 bytes).
    Removing lfs.o(i.lfs_dir_close), (36 bytes).
    Removing lfs.o(i.lfs_dir_commit), (1060 bytes).
    Removing lfs.o(i.lfs_dir_commit_commit), (28 bytes).
    Removing lfs.o(i.lfs_dir_commit_size), (30 bytes).
    Removing lfs.o(i.lfs_dir_commitattr), (200 bytes).
    Removing lfs.o(i.lfs_dir_commitcrc), (444 bytes).
    Removing lfs.o(i.lfs_dir_commitprog), (74 bytes).
    Removing lfs.o(i.lfs_dir_compact), (1240 bytes).
    Removing lfs.o(i.lfs_dir_drop), (100 bytes).
    Removing lfs.o(i.lfs_dir_fetch), (34 bytes).
    Removing lfs.o(i.lfs_dir_fetchmatch), (1220 bytes).
    Removing lfs.o(i.lfs_dir_find), (412 bytes).
    Removing lfs.o(i.lfs_dir_find_match), (114 bytes).
    Removing lfs.o(i.lfs_dir_get), (46 bytes).
    Removing lfs.o(i.lfs_dir_getgstate), (96 bytes).
    Removing lfs.o(i.lfs_dir_getinfo), (184 bytes).
    Removing lfs.o(i.lfs_dir_getread), (302 bytes).
    Removing lfs.o(i.lfs_dir_getslice), (336 bytes).
    Removing lfs.o(i.lfs_dir_open), (188 bytes).
    Removing lfs.o(i.lfs_dir_read), (184 bytes).
    Removing lfs.o(i.lfs_dir_rewind), (46 bytes).
    Removing lfs.o(i.lfs_dir_seek), (114 bytes).
    Removing lfs.o(i.lfs_dir_split), (122 bytes).
    Removing lfs.o(i.lfs_dir_tell), (6 bytes).
    Removing lfs.o(i.lfs_dir_traverse), (568 bytes).
    Removing lfs.o(i.lfs_dir_traverse_filter), (140 bytes).
    Removing lfs.o(i.lfs_file_close), (148 bytes).
    Removing lfs.o(i.lfs_file_flush), (408 bytes).
    Removing lfs.o(i.lfs_file_open), (40 bytes).
    Removing lfs.o(i.lfs_file_opencfg), (692 bytes).
    Removing lfs.o(i.lfs_file_outline), (44 bytes).
    Removing lfs.o(i.lfs_file_read), (468 bytes).
    Removing lfs.o(i.lfs_file_relocate), (404 bytes).
    Removing lfs.o(i.lfs_file_rewind), (32 bytes).
    Removing lfs.o(i.lfs_file_seek), (160 bytes).
    Removing lfs.o(i.lfs_file_size), (108 bytes).
    Removing lfs.o(i.lfs_file_sync), (324 bytes).
    Removing lfs.o(i.lfs_file_tell), (88 bytes).
    Removing lfs.o(i.lfs_file_truncate), (360 bytes).
    Removing lfs.o(i.lfs_file_write), (692 bytes).
    Removing lfs.o(i.lfs_format), (220 bytes).
    Removing lfs.o(i.lfs_free), (12 bytes).
    Removing lfs.o(i.lfs_frombe32), (32 bytes).
    Removing lfs.o(i.lfs_fromle32), (32 bytes).
    Removing lfs.o(i.lfs_fs_demove), (128 bytes).
    Removing lfs.o(i.lfs_fs_deorphan), (416 bytes).
    Removing lfs.o(i.lfs_fs_forceconsistency), (36 bytes).
    Removing lfs.o(i.lfs_fs_parent), (108 bytes).
    Removing lfs.o(i.lfs_fs_parent_match), (88 bytes).
    Removing lfs.o(i.lfs_fs_pred), (78 bytes).
    Removing lfs.o(i.lfs_fs_prepmove), (40 bytes).
    Removing lfs.o(i.lfs_fs_preporphans), (60 bytes).
    Removing lfs.o(i.lfs_fs_relocate), (308 bytes).
    Removing lfs.o(i.lfs_fs_size), (36 bytes).
    Removing lfs.o(i.lfs_fs_size_count), (16 bytes).
    Removing lfs.o(i.lfs_fs_traverse), (344 bytes).
    Removing lfs.o(i.lfs_getattr), (160 bytes).
    Removing lfs.o(i.lfs_gstate_fromle32), (30 bytes).
    Removing lfs.o(i.lfs_gstate_hasmove), (20 bytes).
    Removing lfs.o(i.lfs_gstate_hasmovehere), (32 bytes).
    Removing lfs.o(i.lfs_gstate_hasorphans), (20 bytes).
    Removing lfs.o(i.lfs_gstate_iszero), (26 bytes).
    Removing lfs.o(i.lfs_gstate_tole32), (30 bytes).
    Removing lfs.o(i.lfs_gstate_xormove), (92 bytes).
    Removing lfs.o(i.lfs_gstate_xororphans), (20 bytes).
    Removing lfs.o(i.lfs_init), (1088 bytes).
    Removing lfs.o(i.lfs_malloc), (12 bytes).
    Removing lfs.o(i.lfs_max), (14 bytes).
    Removing lfs.o(i.lfs_min), (14 bytes).
    Removing lfs.o(i.lfs_mkdir), (396 bytes).
    Removing lfs.o(i.lfs_mount), (700 bytes).
    Removing lfs.o(i.lfs_npw2), (14 bytes).
    Removing lfs.o(i.lfs_pair_cmp), (42 bytes).
    Removing lfs.o(i.lfs_pair_fromle32), (22 bytes).
    Removing lfs.o(i.lfs_pair_isnull), (22 bytes).
    Removing lfs.o(i.lfs_pair_swap), (10 bytes).
    Removing lfs.o(i.lfs_pair_tole32), (22 bytes).
    Removing lfs.o(i.lfs_popc), (12 bytes).
    Removing lfs.o(i.lfs_remove), (288 bytes).
    Removing lfs.o(i.lfs_removeattr), (32 bytes).
    Removing lfs.o(i.lfs_rename), (568 bytes).
    Removing lfs.o(i.lfs_setattr), (48 bytes).
    Removing lfs.o(i.lfs_stat), (62 bytes).
    Removing lfs.o(i.lfs_superblock_fromle32), (54 bytes).
    Removing lfs.o(i.lfs_superblock_tole32), (54 bytes).
    Removing lfs.o(i.lfs_tag_chunk), (8 bytes).
    Removing lfs.o(i.lfs_tag_dsize), (22 bytes).
    Removing lfs.o(i.lfs_tag_id), (8 bytes).
    Removing lfs.o(i.lfs_tag_isdelete), (18 bytes).
    Removing lfs.o(i.lfs_tag_isvalid), (18 bytes).
    Removing lfs.o(i.lfs_tag_size), (8 bytes).
    Removing lfs.o(i.lfs_tag_splice), (14 bytes).
    Removing lfs.o(i.lfs_tag_type1), (10 bytes).
    Removing lfs.o(i.lfs_tag_type3), (8 bytes).
    Removing lfs.o(i.lfs_tobe32), (12 bytes).
    Removing lfs.o(i.lfs_tole32), (12 bytes).
    Removing lfs.o(i.lfs_unmount), (16 bytes).
    Removing lfs.o(.constdata), (448 bytes).
    Removing lfs.o(.conststring), (188 bytes).
    Removing lfs_port.o(.rev16_text), (4 bytes).
    Removing lfs_port.o(.revsh_text), (4 bytes).
    Removing lfs_port.o(.rrx_text), (6 bytes).
    Removing lfs_port.o(i.lfs_deskio_erase), (16 bytes).
    Removing lfs_port.o(i.lfs_deskio_prog), (32 bytes).
    Removing lfs_port.o(i.lfs_deskio_read), (32 bytes).
    Removing lfs_port.o(i.lfs_deskio_sync), (6 bytes).
    Removing lfs_port.o(i.lfs_storage_init), (112 bytes).
    Removing lfs_port.o(.bss), (544 bytes).
    Removing lfs_util.o(i.lfs_crc), (60 bytes).
    Removing lfs_util.o(.constdata), (64 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled.o(i.OLED_Allfill), (56 bytes).
    Removing oled.o(i.OLED_Display_Off), (22 bytes).
    Removing oled.o(i.OLED_Display_On), (22 bytes).
    Removing oled.o(i.OLED_Pow), (22 bytes).
    Removing oled.o(i.OLED_ShowFloat), (352 bytes).
    Removing oled.o(i.OLED_ShowHanzi), (100 bytes).
    Removing oled.o(i.OLED_ShowHzbig), (184 bytes).
    Removing oled.o(i.OLED_ShowNum), (136 bytes).
    Removing oled.o(i.OLED_ShowPic), (76 bytes).
    Removing ebtn.o(i.ebtn_combo_btn_add_btn), (32 bytes).
    Removing ebtn.o(i.ebtn_combo_btn_add_btn_by_idx), (32 bytes).
    Removing ebtn.o(i.ebtn_combo_btn_remove_btn), (32 bytes).
    Removing ebtn.o(i.ebtn_combo_btn_remove_btn_by_idx), (32 bytes).
    Removing ebtn.o(i.ebtn_combo_register), (60 bytes).
    Removing ebtn.o(i.ebtn_get_btn_by_key_id), (80 bytes).
    Removing ebtn.o(i.ebtn_get_btn_index_by_btn), (12 bytes).
    Removing ebtn.o(i.ebtn_get_btn_index_by_btn_dyn), (12 bytes).
    Removing ebtn.o(i.ebtn_get_btn_index_by_key_id), (72 bytes).
    Removing ebtn.o(i.ebtn_get_config), (12 bytes).
    Removing ebtn.o(i.ebtn_get_total_btn_cnt), (28 bytes).
    Removing ebtn.o(i.ebtn_is_btn_active), (20 bytes).
    Removing ebtn.o(i.ebtn_is_btn_in_process), (20 bytes).
    Removing ebtn.o(i.ebtn_is_in_process), (132 bytes).
    Removing ebtn.o(i.ebtn_register), (72 bytes).
    Removing ebtn.o(i.ebtn_set_combo_suppress_threshold), (12 bytes).
    Removing ebtn.o(i.ebtn_set_config), (12 bytes).
    Removing sdio_sdcard.o(.rev16_text), (4 bytes).
    Removing sdio_sdcard.o(.revsh_text), (4 bytes).
    Removing sdio_sdcard.o(.rrx_text), (6 bytes).
    Removing sdio_sdcard.o(i.sd_erase), (324 bytes).
    Removing sdio_sdcard.o(i.sd_lock_unlock), (488 bytes).
    Removing sdio_sdcard.o(i.sd_power_off), (14 bytes).
    Removing sdio_sdcard.o(i.sd_sdstatus_get), (384 bytes).
    Removing sdio_sdcard.o(i.sd_transfer_state_get), (20 bytes).
    Removing ff.o(i.dir_read), (86 bytes).
    Removing ff.o(i.dir_remove), (44 bytes).
    Removing ff.o(i.f_chmod), (84 bytes).
    Removing ff.o(i.f_getfree), (276 bytes).
    Removing ff.o(i.f_lseek), (432 bytes).
    Removing ff.o(i.f_mkdir), (384 bytes).
    Removing ff.o(i.f_opendir), (110 bytes).
    Removing ff.o(i.f_readdir), (88 bytes).
    Removing ff.o(i.f_rename), (290 bytes).
    Removing ff.o(i.f_stat), (58 bytes).
    Removing ff.o(i.f_truncate), (156 bytes).
    Removing ff.o(i.f_unlink), (178 bytes).
    Removing ff.o(i.f_utime), (86 bytes).
    Removing ff.o(i.get_fileinfo), (152 bytes).
    Removing diskio.o(.rev16_text), (4 bytes).
    Removing diskio.o(.revsh_text), (4 bytes).
    Removing diskio.o(.rrx_text), (6 bytes).
    Removing btn_app.o(.rev16_text), (4 bytes).
    Removing btn_app.o(.revsh_text), (4 bytes).
    Removing btn_app.o(.rrx_text), (6 bytes).
    Removing led_app.o(.rev16_text), (4 bytes).
    Removing led_app.o(.revsh_text), (4 bytes).
    Removing led_app.o(.rrx_text), (6 bytes).
    Removing oled_app.o(.rev16_text), (4 bytes).
    Removing oled_app.o(.revsh_text), (4 bytes).
    Removing oled_app.o(.rrx_text), (6 bytes).
    Removing scheduler.o(.rev16_text), (4 bytes).
    Removing scheduler.o(.revsh_text), (4 bytes).
    Removing scheduler.o(.rrx_text), (6 bytes).
    Removing usart_app.o(.rev16_text), (4 bytes).
    Removing usart_app.o(.revsh_text), (4 bytes).
    Removing usart_app.o(.rrx_text), (6 bytes).
    Removing sd_app.o(.rev16_text), (4 bytes).
    Removing sd_app.o(.revsh_text), (4 bytes).
    Removing sd_app.o(.rrx_text), (6 bytes).
    Removing rtc_app.o(.rev16_text), (4 bytes).
    Removing rtc_app.o(.revsh_text), (4 bytes).
    Removing rtc_app.o(.rrx_text), (6 bytes).
    Removing adc_app.o(.rev16_text), (4 bytes).
    Removing adc_app.o(.revsh_text), (4 bytes).
    Removing adc_app.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_adc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_adc.o(i.adc_channel_16_to_18), (96 bytes).
    Removing gd32f4xx_adc.o(i.adc_deinit), (20 bytes).
    Removing gd32f4xx_adc.o(i.adc_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_discontinuous_mode_config), (82 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_mode_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_request_after_last_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_end_of_conversion_config), (34 bytes).
    Removing gd32f4xx_adc.o(i.adc_flag_clear), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_flag_get), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_channel_config), (124 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_channel_offset_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_data_read), (46 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_software_startconv_flag_get), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_disable), (66 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_enable), (66 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_flag_clear), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_flag_get), (112 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_config), (58 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_disable), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_enable), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_resolution_config), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_routine_data_read), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_routine_software_startconv_flag_get), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_delay_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_request_after_last_disable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_request_after_last_enable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_routine_data_read), (12 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_disable), (50 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_sequence_channel_enable), (64 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_single_channel_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_single_channel_enable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_threshold_config), (14 bytes).
    Removing gd32f4xx_can.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_can.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_can.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_can.o(i.can1_filter_start_bank), (56 bytes).
    Removing gd32f4xx_can.o(i.can_debug_freeze_disable), (44 bytes).
    Removing gd32f4xx_can.o(i.can_debug_freeze_enable), (44 bytes).
    Removing gd32f4xx_can.o(i.can_deinit), (52 bytes).
    Removing gd32f4xx_can.o(i.can_error_get), (12 bytes).
    Removing gd32f4xx_can.o(i.can_fifo_release), (32 bytes).
    Removing gd32f4xx_can.o(i.can_filter_init), (272 bytes).
    Removing gd32f4xx_can.o(i.can_flag_clear), (16 bytes).
    Removing gd32f4xx_can.o(i.can_flag_get), (30 bytes).
    Removing gd32f4xx_can.o(i.can_init), (290 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_disable), (8 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_enable), (8 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_flag_get), (116 bytes).
    Removing gd32f4xx_can.o(i.can_message_receive), (228 bytes).
    Removing gd32f4xx_can.o(i.can_message_transmit), (336 bytes).
    Removing gd32f4xx_can.o(i.can_receive_error_number_get), (8 bytes).
    Removing gd32f4xx_can.o(i.can_receive_message_length_get), (26 bytes).
    Removing gd32f4xx_can.o(i.can_struct_para_init), (164 bytes).
    Removing gd32f4xx_can.o(i.can_time_trigger_mode_disable), (48 bytes).
    Removing gd32f4xx_can.o(i.can_time_trigger_mode_enable), (48 bytes).
    Removing gd32f4xx_can.o(i.can_transmission_stop), (80 bytes).
    Removing gd32f4xx_can.o(i.can_transmit_error_number_get), (10 bytes).
    Removing gd32f4xx_can.o(i.can_transmit_states), (124 bytes).
    Removing gd32f4xx_can.o(i.can_wakeup), (48 bytes).
    Removing gd32f4xx_can.o(i.can_working_mode_set), (168 bytes).
    Removing gd32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_crc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_crc.o(i.crc_block_data_calculate), (36 bytes).
    Removing gd32f4xx_crc.o(i.crc_data_register_read), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_data_register_reset), (20 bytes).
    Removing gd32f4xx_crc.o(i.crc_deinit), (24 bytes).
    Removing gd32f4xx_crc.o(i.crc_free_data_register_read), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_free_data_register_write), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_single_data_calculate), (16 bytes).
    Removing gd32f4xx_ctc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ctc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ctc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_clock_limit_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_capture_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_direction_read), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_disable), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_enable), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_reload_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_reload_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_deinit), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_flag_clear), (36 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_flag_get), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_hardware_trim_mode_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_disable), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_enable), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_flag_get), (56 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_irc48m_trim_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_irc48m_trim_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_polarity_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_prescaler_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_signal_select), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_software_refsource_pulse_generate), (20 bytes).
    Removing gd32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dac.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_data_set), (48 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_disable), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_enable), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_output_buffer_disable), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_output_buffer_enable), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_software_trigger_enable), (12 bytes).
    Removing gd32f4xx_dac.o(i.dac_data_set), (64 bytes).
    Removing gd32f4xx_dac.o(i.dac_disable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_dma_disable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_flag_clear), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_flag_get), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_disable), (18 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_enable), (18 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_flag_clear), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_flag_get), (38 bytes).
    Removing gd32f4xx_dac.o(i.dac_lfsr_noise_config), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_buffer_disable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_buffer_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_value_get), (22 bytes).
    Removing gd32f4xx_dac.o(i.dac_software_trigger_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_triangle_noise_config), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_disable), (26 bytes).
    Removing gd32f4xx_dbg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dbg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dbg.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_deinit), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_id_get), (12 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_low_power_disable), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_low_power_enable), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_periph_disable), (32 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_periph_enable), (32 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_trace_pin_disable), (20 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_trace_pin_enable), (20 bytes).
    Removing gd32f4xx_dci.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dci.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dci.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_dci.o(i.dci_capture_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_capture_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_config), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_data_read), (12 bytes).
    Removing gd32f4xx_dci.o(i.dci_deinit), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_embedded_sync_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_embedded_sync_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_flag_get), (36 bytes).
    Removing gd32f4xx_dci.o(i.dci_init), (52 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_disable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_enable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_jpeg_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_jpeg_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_sync_codes_config), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_sync_codes_unmask_config), (24 bytes).
    Removing gd32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dma.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_dma.o(i.dma_fifo_status_get), (20 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_disable), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_enable), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_flag_clear), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_flag_get), (516 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_address_generation_config), (64 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_burst_beats_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_width_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_multi_data_para_struct_init), (40 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_address_config), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_burst_beats_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_width_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_peripheral_address_generation_config), (126 bytes).
    Removing gd32f4xx_dma.o(i.dma_priority_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_switch_buffer_mode_config), (76 bytes).
    Removing gd32f4xx_dma.o(i.dma_switch_buffer_mode_enable), (66 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_direction_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_using_memory_get), (28 bytes).
    Removing gd32f4xx_enet.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_enet.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_enet.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_config), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_disable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_enable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_current_desc_address_get), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_debug_status_get), (108 bytes).
    Removing gd32f4xx_enet.o(i.enet_default_init), (152 bytes).
    Removing gd32f4xx_enet.o(i.enet_deinit), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_delay), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_clear), (8 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_get), (14 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_set), (8 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_information_get), (100 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_select_normal_mode), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_descriptors_chain_init), (200 bytes).
    Removing gd32f4xx_enet.o(i.enet_descriptors_ring_init), (236 bytes).
    Removing gd32f4xx_enet.o(i.enet_disable), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_dma_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_dma_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_dmaprocess_resume), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_dmaprocess_state_get), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_enable), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_flag_clear), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_flag_get), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_fliter_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_fliter_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_feature_disable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_feature_enable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_threshold_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_forward_feature_disable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_forward_feature_enable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_frame_receive), (248 bytes).
    Removing gd32f4xx_enet.o(i.enet_frame_transmit), (204 bytes).
    Removing gd32f4xx_enet.o(i.enet_init), (868 bytes).
    Removing gd32f4xx_enet.o(i.enet_initpara_config), (356 bytes).
    Removing gd32f4xx_enet.o(i.enet_initpara_reset), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_disable), (72 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_enable), (72 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_flag_clear), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_mac_address_get), (60 bytes).
    Removing gd32f4xx_enet.o(i.enet_mac_address_set), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_missed_frame_counter_get), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_get), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_preset_config), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_reset), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_feature_disable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_feature_enable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_config), (44 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_detect_config), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_generate), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_phy_config), (216 bytes).
    Removing gd32f4xx_enet.o(i.enet_phy_write_read), (156 bytes).
    Removing gd32f4xx_enet.o(i.enet_phyloopback_disable), (50 bytes).
    Removing gd32f4xx_enet.o(i.enet_phyloopback_enable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_expected_time_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init), (236 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init), (280 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_pps_output_frequency_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_subsecond_increment_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_system_time_get), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_addend_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_function_config), (256 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_update_config), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptpframe_receive_normal_mode), (340 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptpframe_transmit_normal_mode), (444 bytes).
    Removing gd32f4xx_enet.o(i.enet_registers_get), (56 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_desc_delay_receive_complete_interrupt), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_desc_immediate_receive_complete_interrupt), (10 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_disable), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_enable), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxframe_drop), (172 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxframe_size_get), (152 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxprocess_check_recovery), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_software_reset), (60 bytes).
    Removing gd32f4xx_enet.o(i.enet_transmit_checksum_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_tx_disable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_tx_enable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_txfifo_flush), (52 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_filter_config), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_filter_register_pointer_reset), (20 bytes).
    Removing gd32f4xx_enet.o(.bss), (15460 bytes).
    Removing gd32f4xx_enet.o(.constdata), (116 bytes).
    Removing gd32f4xx_enet.o(.data), (20 bytes).
    Removing gd32f4xx_exmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exmc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_ecc_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_flag_clear), (52 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_flag_get), (52 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_disable), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_enable), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_flag_clear), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_flag_get), (72 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_deinit), (42 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_disable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_ecc_config), (48 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_enable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_init), (172 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_struct_para_init), (54 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_consecutive_clock_config), (42 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_deinit), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_disable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_enable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_init), (228 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_page_size_config), (40 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_struct_para_init), (106 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_deinit), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_disable), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_enable), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_init), (188 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_struct_para_init), (60 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_autorefresh_number_set), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_bankstatus_get), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_command_config), (28 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_deinit), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_init), (284 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_readsample_config), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_readsample_enable), (44 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_refresh_count_set), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_struct_command_para_init), (16 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_struct_para_init), (66 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_write_protection_config), (64 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_deinit), (44 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_high_id_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_init), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_low_id_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_read_command_set), (28 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_read_id_command_send), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_send_command_state_get), (48 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_struct_para_init), (20 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_write_cmd_send), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_write_command_set), (28 bytes).
    Removing gd32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exti.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_exti.o(i.exti_deinit), (28 bytes).
    Removing gd32f4xx_exti.o(i.exti_event_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_event_enable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_flag_clear), (12 bytes).
    Removing gd32f4xx_exti.o(i.exti_flag_get), (24 bytes).
    Removing gd32f4xx_exti.o(i.exti_init), (188 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_enable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_exti.o(i.exti_software_interrupt_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_software_interrupt_enable), (16 bytes).
    Removing gd32f4xx_fmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fmc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_bank0_erase), (68 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_bank1_erase), (68 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_byte_program), (80 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_flag_clear), (12 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_flag_get), (24 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_halfword_program), (84 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_disable), (16 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_enable), (16 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_flag_get), (64 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_lock), (20 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_mass_erase), (72 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_page_erase), (124 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_ready_wait), (32 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_sector_erase), (96 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_state_get), (76 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_unlock), (36 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_word_program), (84 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_wscnt_set), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_boot_mode_config), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_double_bank_select), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp0_get), (32 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp1_get), (32 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp_disable), (96 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp_enable), (84 bytes).
    Removing gd32f4xx_fmc.o(i.ob_erase), (76 bytes).
    Removing gd32f4xx_fmc.o(i.ob_lock), (20 bytes).
    Removing gd32f4xx_fmc.o(i.ob_security_protection_config), (40 bytes).
    Removing gd32f4xx_fmc.o(i.ob_spc_get), (28 bytes).
    Removing gd32f4xx_fmc.o(i.ob_start), (20 bytes).
    Removing gd32f4xx_fmc.o(i.ob_unlock), (36 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_bor_threshold), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_bor_threshold_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_write), (52 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection0_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection1_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection_disable), (72 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection_enable), (72 bytes).
    Removing gd32f4xx_fwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_config), (104 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_counter_reload), (16 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_enable), (16 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_flag_get), (24 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_prescaler_value_config), (60 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_reload_value_config), (64 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_write_disable), (12 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_write_enable), (16 bytes).
    Removing gd32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_gpio.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_bit_toggle), (4 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_bit_write), (10 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_deinit), (206 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_input_port_get), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_output_bit_get), (16 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_output_port_get), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_pin_lock), (18 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_port_toggle), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_port_write), (4 bytes).
    Removing gd32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_i2c.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_ackpos_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_analog_noise_filter_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_analog_noise_filter_enable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_data_receive), (8 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_data_transmit), (6 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_digital_noise_filter_config), (8 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dma_last_transfer_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dualaddr_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dualaddr_enable), (12 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_disable), (26 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_enable), (26 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_flag_clear), (44 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_flag_get), (92 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_transfer_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_value_get), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_disable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_enable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_timeout_disable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_timeout_enable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_slave_response_to_gcall_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_alert_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_arp_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_type_config), (24 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_software_reset_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_stretch_scl_low_config), (16 bytes).
    Removing gd32f4xx_ipa.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ipa.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ipa.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_init), (164 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_lut_init), (100 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_lut_loading_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_struct_para_init), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_deinit), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_destination_init), (316 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_destination_struct_para_init), (22 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_flag_clear), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_flag_get), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_init), (164 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_lut_init), (100 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_lut_loading_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_struct_para_init), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_inter_timer_config), (36 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_disable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_enable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interval_clock_num_config), (28 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_line_mark_config), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_pixel_format_convert_mode_set), (28 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_hangup_disable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_hangup_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_stop_disable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_stop_enable), (20 bytes).
    Removing gd32f4xx_iref.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_iref.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_iref.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_iref.o(i.iref_deinit), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_disable), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_enable), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_mode_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_precision_trim_value_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_sink_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_step_data_config), (28 bytes).
    Removing gd32f4xx_misc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_misc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_misc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_misc.o(i.nvic_irq_disable), (24 bytes).
    Removing gd32f4xx_misc.o(i.nvic_vector_table_set), (24 bytes).
    Removing gd32f4xx_misc.o(i.system_lowpower_reset), (16 bytes).
    Removing gd32f4xx_misc.o(i.system_lowpower_set), (16 bytes).
    Removing gd32f4xx_misc.o(i.systick_clksource_set), (40 bytes).
    Removing gd32f4xx_pmu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_pmu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_pmu.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_ldo_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_write_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_deinit), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_flag_clear), (48 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_flag_get), (24 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_mode_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_mode_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_switch_select), (44 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_ldo_output_select), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowdriver_mode_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowdriver_mode_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowpower_driver_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lvd_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lvd_select), (48 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_normalpower_driver_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_deepsleepmode), (244 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_sleepmode), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_standbymode), (108 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_wakeup_pin_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_wakeup_pin_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(.bss), (16 bytes).
    Removing gd32f4xx_rcu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rcu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rcu.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ahb_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_apb1_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_apb2_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_bkp_reset_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_bkp_reset_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ck48m_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ckout0_config), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ckout1_config), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_deepsleep_voltage_set), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_deinit), (140 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_hxtal_clock_monitor_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_hxtal_clock_monitor_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_i2s_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_enable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_irc16m_adjust_value_set), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_lxtal_drive_capability_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_bypass_mode_disable), (116 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_bypass_mode_enable), (116 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_off), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_sleep_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_sleep_enable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pll48m_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pll_config), (132 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_plli2s_config), (44 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pllsai_config), (72 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_rtc_div_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_config), (32 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_system_clock_source_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_system_clock_source_get), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_timer_clock_prescaler_config), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_tli_clock_div_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_voltage_key_unlock), (16 bytes).
    Removing gd32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rtc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_config), (100 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_disable), (128 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_enable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_get), (68 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_output_config), (84 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_subsecond_config), (52 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_subsecond_get), (32 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_bypass_shadow_disable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_bypass_shadow_enable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_calibration_output_config), (48 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_config), (116 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_deinit), (204 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_flag_clear), (16 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_flag_get), (20 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_hour_adjust), (36 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_interrupt_disable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_interrupt_enable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_refclock_detection_disable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_refclock_detection_enable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_second_adjust), (108 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_smooth_calibration_config), (80 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_subsecond_get), (20 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper0_pin_map), (28 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper_disable), (16 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper_enable), (200 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_disable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_enable), (48 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_get), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_pin_map), (28 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_subsecond_get), (12 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_clock_set), (92 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_disable), (84 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_enable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_timer_get), (12 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_timer_set), (76 bytes).
    Removing gd32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_sdio.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_completion_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_completion_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_interrupt_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_interrupt_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_clock_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_csm_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_counter_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_fifo_counter_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_hardware_clock_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_operation_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_operation_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_type_set), (40 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_stop_readwait_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_stop_readwait_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_suspend_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_suspend_enable), (20 bytes).
    Removing gd32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_spi.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_spi.o(i.i2s_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.i2s_enable), (10 bytes).
    Removing gd32f4xx_spi.o(i.i2s_full_duplex_mode_config), (48 bytes).
    Removing gd32f4xx_spi.o(i.i2s_init), (28 bytes).
    Removing gd32f4xx_spi.o(i.i2s_psc_config), (292 bytes).
    Removing gd32f4xx_spi.o(i.spi_bidirectional_transfer_config), (26 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_error_clear), (8 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_get), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_next), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_off), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_on), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_polynomial_get), (8 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_polynomial_set), (4 bytes).
    Removing gd32f4xx_spi.o(i.spi_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_data_frame_format_config), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_data_receive), (8 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_data_transmit), (4 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_deinit), (172 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_flag_get), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_format_error_clear), (6 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_disable), (48 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_enable), (48 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_flag_get), (112 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_internal_high), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_internal_low), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_output_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_output_enable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_disable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_io23_output_disable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_io23_output_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_read_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_write_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_struct_para_init), (18 bytes).
    Removing gd32f4xx_spi.o(i.spi_ti_mode_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_ti_mode_enable), (10 bytes).
    Removing gd32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_syscfg.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_bootmode_config), (28 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_compensation_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_deinit), (20 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_enet_phy_interface_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_exmc_swap_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_exti_line_config), (172 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_flag_get), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_fmc_swap_config), (24 bytes).
    Removing gd32f4xx_timer.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_timer.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_timer.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_timer.o(i.timer_auto_reload_shadow_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_auto_reload_shadow_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_automatic_output_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_automatic_output_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_autoreload_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_config), (30 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_struct_para_init), (18 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_capture_value_register_read), (42 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_complementary_output_polarity_config), (70 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_complementary_output_state_config), (70 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_control_shadow_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_control_shadow_update_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_dma_request_source_select), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_input_struct_para_init), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_clear_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_config), (492 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_fast_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_mode_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_polarity_config), (92 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_pulse_value_config), (38 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_shadow_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_state_config), (92 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_struct_para_init), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_remap_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_alignment), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_down_direction), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_read), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_up_direction), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_disable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_enable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_transfer_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_event_software_generate), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode0_config), (40 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode1_config), (32 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode1_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_trigger_as_external_clock_config), (166 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_trigger_config), (30 bytes).
    Removing gd32f4xx_timer.o(i.timer_flag_clear), (6 bytes).
    Removing gd32f4xx_timer.o(i.timer_flag_get), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_hall_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_capture_config), (326 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_pwm_capture_config), (356 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_trigger_source_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_internal_clock_config), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_internal_trigger_as_external_clock_config), (32 bytes).
    Removing gd32f4xx_timer.o(i.timer_interrupt_disable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_interrupt_enable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_interrupt_flag_clear), (6 bytes).
    Removing gd32f4xx_timer.o(i.timer_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_master_slave_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_output_value_selection_config), (34 bytes).
    Removing gd32f4xx_timer.o(i.timer_prescaler_config), (14 bytes).
    Removing gd32f4xx_timer.o(i.timer_prescaler_read), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_primary_output_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_quadrature_decoder_mode_config), (64 bytes).
    Removing gd32f4xx_timer.o(i.timer_repetition_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_single_pulse_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_slave_mode_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_event_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_event_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_source_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_write_chxval_register_config), (34 bytes).
    Removing gd32f4xx_tli.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_tli.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_tli.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_init), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_current_pos_get), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_deinit), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_disable), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_dither_config), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_enable), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_flag_get), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_init), (188 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_disable), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_enable), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_init), (152 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_struct_para_init), (48 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_window_offset_modify), (228 bytes).
    Removing gd32f4xx_tli.o(i.tli_line_mark_set), (24 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_init), (28 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_struct_para_init), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_reload_config), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_struct_para_init), (34 bytes).
    Removing gd32f4xx_trng.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_trng.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_trng.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_trng.o(i.trng_deinit), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_disable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_enable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_flag_get), (24 bytes).
    Removing gd32f4xx_trng.o(i.trng_get_true_random_data), (12 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_disable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_enable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_usart.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_usart.o(i.usart_address_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_block_length_config), (28 bytes).
    Removing gd32f4xx_usart.o(i.usart_break_frame_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_data_first_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_dma_transmit_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_flag_clear), (52 bytes).
    Removing gd32f4xx_usart.o(i.usart_guard_time_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_halfduplex_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_halfduplex_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_cts_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_rts_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_interrupt_disable), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_interrupt_flag_clear), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_invert_config), (104 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_lowpower_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_break_detection_length_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_wakeup_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_oversample_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_parity_check_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_parity_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_prescaler_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_disable), (14 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_enable), (14 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_threshold_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_sample_bit_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_send_break), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_autoretry_config), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_nack_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_nack_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_stop_bit_set), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_config), (34 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_word_length_set), (16 bytes).
    Removing gd32f4xx_wwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_config), (28 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_counter_update), (16 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_deinit), (20 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_enable), (20 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_flag_clear), (12 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_flag_get), (24 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_interrupt_enable), (20 bytes).
    Removing system_gd32f4xx.o(.rev16_text), (4 bytes).
    Removing system_gd32f4xx.o(.revsh_text), (4 bytes).
    Removing system_gd32f4xx.o(.rrx_text), (6 bytes).
    Removing system_gd32f4xx.o(i.SystemCoreClockUpdate), (272 bytes).
    Removing perf_counter.o(.rev16_text), (4 bytes).
    Removing perf_counter.o(.revsh_text), (4 bytes).
    Removing perf_counter.o(.rrx_text), (6 bytes).
    Removing perf_counter.o(i.EventRecorderTimerGetCount), (8 bytes).
    Removing perf_counter.o(i.EventRecorderTimerGetFreq), (8 bytes).
    Removing perf_counter.o(i.EventRecorderTimerSetup), (4 bytes).
    Removing perf_counter.o(i.__on_context_switch_in), (76 bytes).
    Removing perf_counter.o(i.__on_context_switch_out), (112 bytes).
    Removing perf_counter.o(i.__perfc_is_time_out), (100 bytes).
    Removing perf_counter.o(i.__start_task_cycle_counter), (80 bytes).
    Removing perf_counter.o(i.__stop_task_cycle_counter), (148 bytes).
    Removing perf_counter.o(i.before_cycle_counter_reconfiguration), (64 bytes).
    Removing perf_counter.o(i.clock), (8 bytes).
    Removing perf_counter.o(i.delay_us), (84 bytes).
    Removing perf_counter.o(i.disable_task_cycle_info), (54 bytes).
    Removing perf_counter.o(i.enable_task_cycle_info), (58 bytes).
    Removing perf_counter.o(i.get_rtos_task_cycle_info), (4 bytes).
    Removing perf_counter.o(i.get_system_us), (128 bytes).
    Removing perf_counter.o(i.init_task_cycle_counter), (44 bytes).
    Removing perf_counter.o(i.init_task_cycle_info), (38 bytes).
    Removing perf_counter.o(i.perfc_check_task_stack_canary_safe), (52 bytes).
    Removing perf_counter.o(i.perfc_convert_ms_to_ticks), (44 bytes).
    Removing perf_counter.o(i.perfc_convert_ticks_to_ms), (28 bytes).
    Removing perf_counter.o(i.perfc_convert_ticks_to_us), (28 bytes).
    Removing perf_counter.o(i.perfc_convert_us_to_ticks), (44 bytes).
    Removing perf_counter.o(i.perfc_get_systimer_frequency), (8 bytes).
    Removing perf_counter.o(i.register_task_cycle_agent), (92 bytes).
    Removing perf_counter.o(i.resume_task_cycle_info), (16 bytes).
    Removing perf_counter.o(i.unregister_task_cycle_agent), (74 bytes).
    Removing perfc_port_default.o(.rev16_text), (4 bytes).
    Removing perfc_port_default.o(.revsh_text), (4 bytes).
    Removing perfc_port_default.o(.rrx_text), (6 bytes).
    Removing perfc_port_default.o(i.perfc_port_clear_system_timer_counter), (10 bytes).
    Removing perfc_port_default.o(i.perfc_port_stop_system_timer_counting), (18 bytes).
    Removing lfs.o(i.__ARM_pop), (48 bytes).

1098 unused section(s) (total 79244 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/dczerorl2.s                0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_heap_descriptor_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_heap_descriptor.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  mutex_dummy.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_io.o ABSOLUTE
    ../clib/arm_runtime.c                    0x00000000   Number         0  init_aeabi.o ABSOLUTE
    ../clib/arm_runtime.c                    0x00000000   Number         0  init_aeabi.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv.o ABSOLUTE
    ../clib/assert.c                         0x00000000   Number         0  assert.o ABSOLUTE
    ../clib/assert.c                         0x00000000   Number         0  assert_puts.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_alloc.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_init.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_final.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_init_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_free_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_alloc_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_free.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_extend.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_extend_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_final_mt.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  heap2.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  fdtree.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  heap2mt.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc2.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  free.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  term_alloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  heapstubs.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  init_alloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hguard.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc2.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc1.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc1.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxa.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llsdiv.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  aeabi_memset.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsnprintf.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fclose.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  setvbuf.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fseek.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  streamlock.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  stdio.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ftell.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fopen.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  setvbuf_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fopen_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  initio.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  stdio_streams.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  initio_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  abort.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strcspn.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strchr.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strspn.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ..\APP\adc_app.c                         0x00000000   Number         0  adc_app.o ABSOLUTE
    ..\APP\btn_app.c                         0x00000000   Number         0  btn_app.o ABSOLUTE
    ..\APP\led_app.c                         0x00000000   Number         0  led_app.o ABSOLUTE
    ..\APP\oled_app.c                        0x00000000   Number         0  oled_app.o ABSOLUTE
    ..\APP\rtc_app.c                         0x00000000   Number         0  rtc_app.o ABSOLUTE
    ..\APP\scheduler.c                       0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\APP\sd_app.c                          0x00000000   Number         0  sd_app.o ABSOLUTE
    ..\APP\usart_app.c                       0x00000000   Number         0  usart_app.o ABSOLUTE
    ..\Components\bsp\mcu_cmic_gd32f470vet6.c 0x00000000   Number         0  mcu_cmic_gd32f470vet6.o ABSOLUTE
    ..\Components\ebtn\ebtn.c                0x00000000   Number         0  ebtn.o ABSOLUTE
    ..\Components\fatfs\diskio.c             0x00000000   Number         0  diskio.o ABSOLUTE
    ..\Components\fatfs\ff.c                 0x00000000   Number         0  ff.o ABSOLUTE
    ..\Components\gd25qxx\gd25qxx.c          0x00000000   Number         0  gd25qxx.o ABSOLUTE
    ..\Components\gd25qxx\lfs.c              0x00000000   Number         0  lfs.o ABSOLUTE
    ..\Components\gd25qxx\lfs_port.c         0x00000000   Number         0  lfs_port.o ABSOLUTE
    ..\Components\gd25qxx\lfs_util.c         0x00000000   Number         0  lfs_util.o ABSOLUTE
    ..\Components\oled\oled.c                0x00000000   Number         0  oled.o ABSOLUTE
    ..\Components\sdio\sdio_sdcard.c         0x00000000   Number         0  sdio_sdcard.o ABSOLUTE
    ..\Driver\CMSIS\GD\GD32F4xx\Source\system_gd32f4xx.c 0x00000000   Number         0  system_gd32f4xx.o ABSOLUTE
    ..\Libraries\Source\gd32f4xx_adc.c       0x00000000   Number         0  gd32f4xx_adc.o ABSOLUTE
    ..\Libraries\Source\gd32f4xx_can.c       0x00000000   Number         0  gd32f4xx_can.o ABSOLUTE
    ..\Libraries\Source\gd32f4xx_crc.c       0x00000000   Number         0  gd32f4xx_crc.o ABSOLUTE
    ..\Libraries\Source\gd32f4xx_ctc.c       0x00000000   Number         0  gd32f4xx_ctc.o ABSOLUTE
    ..\Libraries\Source\gd32f4xx_dac.c       0x00000000   Number         0  gd32f4xx_dac.o ABSOLUTE
    ..\Libraries\Source\gd32f4xx_dbg.c       0x00000000   Number         0  gd32f4xx_dbg.o ABSOLUTE
    ..\Libraries\Source\gd32f4xx_dci.c       0x00000000   Number         0  gd32f4xx_dci.o ABSOLUTE
    ..\Libraries\Source\gd32f4xx_dma.c       0x00000000   Number         0  gd32f4xx_dma.o ABSOLUTE
    ..\Libraries\Source\gd32f4xx_enet.c      0x00000000   Number         0  gd32f4xx_enet.o ABSOLUTE
    ..\Libraries\Source\gd32f4xx_exmc.c      0x00000000   Number         0  gd32f4xx_exmc.o ABSOLUTE
    ..\Libraries\Source\gd32f4xx_exti.c      0x00000000   Number         0  gd32f4xx_exti.o ABSOLUTE
    ..\Libraries\Source\gd32f4xx_fmc.c       0x00000000   Number         0  gd32f4xx_fmc.o ABSOLUTE
    ..\Libraries\Source\gd32f4xx_fwdgt.c     0x00000000   Number         0  gd32f4xx_fwdgt.o ABSOLUTE
    ..\Libraries\Source\gd32f4xx_gpio.c      0x00000000   Number         0  gd32f4xx_gpio.o ABSOLUTE
    ..\Libraries\Source\gd32f4xx_i2c.c       0x00000000   Number         0  gd32f4xx_i2c.o ABSOLUTE
    ..\Libraries\Source\gd32f4xx_ipa.c       0x00000000   Number         0  gd32f4xx_ipa.o ABSOLUTE
    ..\Libraries\Source\gd32f4xx_iref.c      0x00000000   Number         0  gd32f4xx_iref.o ABSOLUTE
    ..\Libraries\Source\gd32f4xx_misc.c      0x00000000   Number         0  gd32f4xx_misc.o ABSOLUTE
    ..\Libraries\Source\gd32f4xx_pmu.c       0x00000000   Number         0  gd32f4xx_pmu.o ABSOLUTE
    ..\Libraries\Source\gd32f4xx_rcu.c       0x00000000   Number         0  gd32f4xx_rcu.o ABSOLUTE
    ..\Libraries\Source\gd32f4xx_rtc.c       0x00000000   Number         0  gd32f4xx_rtc.o ABSOLUTE
    ..\Libraries\Source\gd32f4xx_sdio.c      0x00000000   Number         0  gd32f4xx_sdio.o ABSOLUTE
    ..\Libraries\Source\gd32f4xx_spi.c       0x00000000   Number         0  gd32f4xx_spi.o ABSOLUTE
    ..\Libraries\Source\gd32f4xx_syscfg.c    0x00000000   Number         0  gd32f4xx_syscfg.o ABSOLUTE
    ..\Libraries\Source\gd32f4xx_timer.c     0x00000000   Number         0  gd32f4xx_timer.o ABSOLUTE
    ..\Libraries\Source\gd32f4xx_tli.c       0x00000000   Number         0  gd32f4xx_tli.o ABSOLUTE
    ..\Libraries\Source\gd32f4xx_trng.c      0x00000000   Number         0  gd32f4xx_trng.o ABSOLUTE
    ..\Libraries\Source\gd32f4xx_usart.c     0x00000000   Number         0  gd32f4xx_usart.o ABSOLUTE
    ..\Libraries\Source\gd32f4xx_wwdgt.c     0x00000000   Number         0  gd32f4xx_wwdgt.o ABSOLUTE
    ..\Libraries\startup_gd32f450_470.s      0x00000000   Number         0  startup_gd32f450_470.o ABSOLUTE
    ..\USER\src\gd32f4xx_it.c                0x00000000   Number         0  gd32f4xx_it.o ABSOLUTE
    ..\USER\src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\USER\src\systick.c                    0x00000000   Number         0  systick.o ABSOLUTE
    ..\\APP\\adc_app.c                       0x00000000   Number         0  adc_app.o ABSOLUTE
    ..\\APP\\btn_app.c                       0x00000000   Number         0  btn_app.o ABSOLUTE
    ..\\APP\\led_app.c                       0x00000000   Number         0  led_app.o ABSOLUTE
    ..\\APP\\oled_app.c                      0x00000000   Number         0  oled_app.o ABSOLUTE
    ..\\APP\\rtc_app.c                       0x00000000   Number         0  rtc_app.o ABSOLUTE
    ..\\APP\\scheduler.c                     0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\\APP\\sd_app.c                        0x00000000   Number         0  sd_app.o ABSOLUTE
    ..\\APP\\usart_app.c                     0x00000000   Number         0  usart_app.o ABSOLUTE
    ..\\Components\\bsp\\mcu_cmic_gd32f470vet6.c 0x00000000   Number         0  mcu_cmic_gd32f470vet6.o ABSOLUTE
    ..\\Components\\fatfs\\diskio.c          0x00000000   Number         0  diskio.o ABSOLUTE
    ..\\Components\\gd25qxx\\gd25qxx.c       0x00000000   Number         0  gd25qxx.o ABSOLUTE
    ..\\Components\\gd25qxx\\lfs_port.c      0x00000000   Number         0  lfs_port.o ABSOLUTE
    ..\\Components\\oled\\oled.c             0x00000000   Number         0  oled.o ABSOLUTE
    ..\\Components\\sdio\\sdio_sdcard.c      0x00000000   Number         0  sdio_sdcard.o ABSOLUTE
    ..\\Driver\\CMSIS\\GD\\GD32F4xx\\Source\\system_gd32f4xx.c 0x00000000   Number         0  system_gd32f4xx.o ABSOLUTE
    ..\\Libraries\\Source\\gd32f4xx_adc.c    0x00000000   Number         0  gd32f4xx_adc.o ABSOLUTE
    ..\\Libraries\\Source\\gd32f4xx_can.c    0x00000000   Number         0  gd32f4xx_can.o ABSOLUTE
    ..\\Libraries\\Source\\gd32f4xx_crc.c    0x00000000   Number         0  gd32f4xx_crc.o ABSOLUTE
    ..\\Libraries\\Source\\gd32f4xx_ctc.c    0x00000000   Number         0  gd32f4xx_ctc.o ABSOLUTE
    ..\\Libraries\\Source\\gd32f4xx_dac.c    0x00000000   Number         0  gd32f4xx_dac.o ABSOLUTE
    ..\\Libraries\\Source\\gd32f4xx_dbg.c    0x00000000   Number         0  gd32f4xx_dbg.o ABSOLUTE
    ..\\Libraries\\Source\\gd32f4xx_dci.c    0x00000000   Number         0  gd32f4xx_dci.o ABSOLUTE
    ..\\Libraries\\Source\\gd32f4xx_dma.c    0x00000000   Number         0  gd32f4xx_dma.o ABSOLUTE
    ..\\Libraries\\Source\\gd32f4xx_enet.c   0x00000000   Number         0  gd32f4xx_enet.o ABSOLUTE
    ..\\Libraries\\Source\\gd32f4xx_exmc.c   0x00000000   Number         0  gd32f4xx_exmc.o ABSOLUTE
    ..\\Libraries\\Source\\gd32f4xx_exti.c   0x00000000   Number         0  gd32f4xx_exti.o ABSOLUTE
    ..\\Libraries\\Source\\gd32f4xx_fmc.c    0x00000000   Number         0  gd32f4xx_fmc.o ABSOLUTE
    ..\\Libraries\\Source\\gd32f4xx_fwdgt.c  0x00000000   Number         0  gd32f4xx_fwdgt.o ABSOLUTE
    ..\\Libraries\\Source\\gd32f4xx_gpio.c   0x00000000   Number         0  gd32f4xx_gpio.o ABSOLUTE
    ..\\Libraries\\Source\\gd32f4xx_i2c.c    0x00000000   Number         0  gd32f4xx_i2c.o ABSOLUTE
    ..\\Libraries\\Source\\gd32f4xx_ipa.c    0x00000000   Number         0  gd32f4xx_ipa.o ABSOLUTE
    ..\\Libraries\\Source\\gd32f4xx_iref.c   0x00000000   Number         0  gd32f4xx_iref.o ABSOLUTE
    ..\\Libraries\\Source\\gd32f4xx_misc.c   0x00000000   Number         0  gd32f4xx_misc.o ABSOLUTE
    ..\\Libraries\\Source\\gd32f4xx_pmu.c    0x00000000   Number         0  gd32f4xx_pmu.o ABSOLUTE
    ..\\Libraries\\Source\\gd32f4xx_rcu.c    0x00000000   Number         0  gd32f4xx_rcu.o ABSOLUTE
    ..\\Libraries\\Source\\gd32f4xx_rtc.c    0x00000000   Number         0  gd32f4xx_rtc.o ABSOLUTE
    ..\\Libraries\\Source\\gd32f4xx_sdio.c   0x00000000   Number         0  gd32f4xx_sdio.o ABSOLUTE
    ..\\Libraries\\Source\\gd32f4xx_spi.c    0x00000000   Number         0  gd32f4xx_spi.o ABSOLUTE
    ..\\Libraries\\Source\\gd32f4xx_syscfg.c 0x00000000   Number         0  gd32f4xx_syscfg.o ABSOLUTE
    ..\\Libraries\\Source\\gd32f4xx_timer.c  0x00000000   Number         0  gd32f4xx_timer.o ABSOLUTE
    ..\\Libraries\\Source\\gd32f4xx_tli.c    0x00000000   Number         0  gd32f4xx_tli.o ABSOLUTE
    ..\\Libraries\\Source\\gd32f4xx_trng.c   0x00000000   Number         0  gd32f4xx_trng.o ABSOLUTE
    ..\\Libraries\\Source\\gd32f4xx_usart.c  0x00000000   Number         0  gd32f4xx_usart.o ABSOLUTE
    ..\\Libraries\\Source\\gd32f4xx_wwdgt.c  0x00000000   Number         0  gd32f4xx_wwdgt.o ABSOLUTE
    ..\\USER\\src\\gd32f4xx_it.c             0x00000000   Number         0  gd32f4xx_it.o ABSOLUTE
    ..\\USER\\src\\main.c                    0x00000000   Number         0  main.o ABSOLUTE
    ..\\USER\\src\\systick.c                 0x00000000   Number         0  systick.o ABSOLUTE
    D:\\keil-MDK\\Keil_MDK\\PACK\\GorgonMeducer\\perf_counter\\2.4.0\\perf_counter.c 0x00000000   Number         0  perf_counter.o ABSOLUTE
    D:\\keil-MDK\\Keil_MDK\\PACK\\GorgonMeducer\\perf_counter\\2.4.0\\perfc_port_default.c 0x00000000   Number         0  perfc_port_default.o ABSOLUTE
    D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\perf_counter.c 0x00000000   Number         0  perf_counter.o ABSOLUTE
    D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\perfc_port_default.c 0x00000000   Number         0  perfc_port_default.o ABSOLUTE
    D:\keil-MDK\Keil_MDK\PACK\GorgonMeducer\perf_counter\2.4.0\systick_wrapper_ual.s 0x00000000   Number         0  systick_wrapper_ual.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      428  startup_gd32f450_470.o(RESET)
    !!!main                                  0x080001ac   Section        8  __main.o(!!!main)
    !!!scatter                               0x080001b4   Section       52  __scatter.o(!!!scatter)
    !!dczerorl2                              0x080001e8   Section       90  __dczerorl2.o(!!dczerorl2)
    !!handler_zi                             0x08000244   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x08000260   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000001  0x08000260   Section        6  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    .ARM.Collect$$_printf_percent$$00000002  0x08000266   Section        6  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    .ARM.Collect$$_printf_percent$$00000003  0x0800026c   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000004  0x08000272   Section        6  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    .ARM.Collect$$_printf_percent$$00000005  0x08000278   Section        6  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    .ARM.Collect$$_printf_percent$$00000006  0x0800027e   Section        6  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    .ARM.Collect$$_printf_percent$$00000007  0x08000284   Section       10  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    .ARM.Collect$$_printf_percent$$00000008  0x0800028e   Section        6  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    .ARM.Collect$$_printf_percent$$00000009  0x08000294   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x0800029a   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000B  0x080002a0   Section        6  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    .ARM.Collect$$_printf_percent$$0000000C  0x080002a6   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$0000000D  0x080002ac   Section        6  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    .ARM.Collect$$_printf_percent$$0000000E  0x080002b2   Section        6  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    .ARM.Collect$$_printf_percent$$0000000F  0x080002b8   Section        6  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    .ARM.Collect$$_printf_percent$$00000010  0x080002be   Section        6  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    .ARM.Collect$$_printf_percent$$00000011  0x080002c4   Section        6  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    .ARM.Collect$$_printf_percent$$00000012  0x080002ca   Section       10  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    .ARM.Collect$$_printf_percent$$00000013  0x080002d4   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x080002da   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000015  0x080002e0   Section        6  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    .ARM.Collect$$_printf_percent$$00000016  0x080002e6   Section        6  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    .ARM.Collect$$_printf_percent$$00000017  0x080002ec   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x080002f0   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x080002f2   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x080002f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080002f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080002f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080002f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x080002f6   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x080002fc   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x080002fc   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x08000308   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000308   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x08000308   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000031          0x08000312   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000031)
    .ARM.Collect$$libinit$$00000032          0x08000316   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000316   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000318   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x0800031a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x0800031a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x0800031a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x0800031a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x0800031a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x0800031a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x0800031a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x0800031a   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x0800031c   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x0800031c   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x0800031c   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000322   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000322   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000326   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000326   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800032e   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000330   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000330   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000334   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x0800033c   Section       64  startup_gd32f450_470.o(.text)
    $v0                                      0x0800033c   Number         0  startup_gd32f450_470.o(.text)
    .text                                    0x0800037c   Section       32  systick_wrapper_ual.o(.text)
    $v0                                      0x0800037c   Number         0  systick_wrapper_ual.o(.text)
    .text                                    0x0800039c   Section       72  llsdiv.o(.text)
    .text                                    0x080003e4   Section        0  vsnprintf.o(.text)
    .text                                    0x08000418   Section        0  __2sprintf.o(.text)
    .text                                    0x08000444   Section        0  _printf_dec.o(.text)
    .text                                    0x080004bc   Section        0  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_common                       0x080004bd   Thumb Code     0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x08000550   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x080006d8   Section        0  memcmp.o(.text)
    .text                                    0x08000730   Section        0  strlen.o(.text)
    .text                                    0x0800076e   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x080007f8   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x0800085c   Section       68  rt_memclr.o(.text)
    .text                                    0x080008a0   Section       78  rt_memclr_w.o(.text)
    .text                                    0x080008ee   Section        0  heapauxi.o(.text)
    .text                                    0x080008f4   Section        0  init_aeabi.o(.text)
    .text                                    0x08000918   Section      238  lludivv7m.o(.text)
    .text                                    0x08000a06   Section        0  _printf_pad.o(.text)
    .text                                    0x08000a54   Section        0  _printf_truncate.o(.text)
    .text                                    0x08000a78   Section        0  _printf_str.o(.text)
    .text                                    0x08000aca   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08000b7c   Section        0  _printf_charcount.o(.text)
    .text                                    0x08000ba4   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000ba5   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000bd4   Section        0  _sputc.o(.text)
    .text                                    0x08000bde   Section        0  _snputc.o(.text)
    .text                                    0x08000bf0   Section        0  _printf_wctomb.o(.text)
    .text                                    0x08000cac   Section        0  _printf_longlong_dec.o(.text)
    .text                                    0x08000d28   Section        0  _printf_oct_int_ll.o(.text)
    _printf_longlong_oct_internal            0x08000d29   Thumb Code     0  _printf_oct_int_ll.o(.text)
    .text                                    0x08000d98   Section        0  sys_exit.o(.text)
    .text                                    0x08000da4   Section        8  libspace.o(.text)
    .text                                    0x08000dac   Section      138  lludiv10.o(.text)
    .text                                    0x08000e36   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08000e39   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08001254   Section        0  _printf_fp_hex.o(.text)
    .text                                    0x08001550   Section        0  _printf_char.o(.text)
    .text                                    0x0800157c   Section        0  _printf_wchar.o(.text)
    .text                                    0x080015a8   Section        0  _wcrtomb.o(.text)
    .text                                    0x080015e8   Section        2  use_no_semi.o(.text)
    .text                                    0x080015ea   Section        0  indicate_semi.o(.text)
    .text                                    0x080015ea   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08001634   Section       16  rt_ctype_table.o(.text)
    .text                                    0x08001644   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x0800164c   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x080016cc   Section        0  bigflt0.o(.text)
    .text                                    0x080017b0   Section        0  exit.o(.text)
    .text                                    0x080017c4   Section      128  strcmpv7m.o(.text)
    CL$$btod_d2e                             0x08001844   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x08001882   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x080018c8   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08001928   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x08001c60   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08001d3c   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x08001d66   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x08001d90   Section      580  btod.o(CL$$btod_mult_common)
    i.BusFault_Handler                       0x08001fd4   Section        0  gd32f4xx_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x08001fd8   Section        0  gd32f4xx_it.o(i.DebugMon_Handler)
    i.HardFault_Handler                      0x08001fdc   Section        0  gd32f4xx_it.o(i.HardFault_Handler)
    i.I2C_Bus_Reset                          0x08001fe0   Section        0  oled.o(i.I2C_Bus_Reset)
    I2C_Bus_Reset                            0x08001fe1   Thumb Code   282  oled.o(i.I2C_Bus_Reset)
    i.MemManage_Handler                      0x08002108   Section        0  gd32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x0800210c   Section        0  gd32f4xx_it.o(i.NMI_Handler)
    i.OLED_Clear                             0x08002110   Section        0  oled.o(i.OLED_Clear)
    i.OLED_Init                              0x08002148   Section        0  oled.o(i.OLED_Init)
    i.OLED_Set_Position                      0x08002178   Section        0  oled.o(i.OLED_Set_Position)
    i.OLED_ShowChar                          0x0800219c   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowStr                           0x08002238   Section        0  oled.o(i.OLED_ShowStr)
    i.OLED_Write_cmd                         0x08002274   Section        0  oled.o(i.OLED_Write_cmd)
    i.OLED_Write_data                        0x0800239c   Section        0  oled.o(i.OLED_Write_data)
    i.PendSV_Handler                         0x080024c4   Section        0  gd32f4xx_it.o(i.PendSV_Handler)
    i.SDIO_IRQHandler                        0x080024c8   Section        0  gd32f4xx_it.o(i.SDIO_IRQHandler)
    i.SVC_Handler                            0x080024d0   Section        0  gd32f4xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x080024d4   Section        0  gd32f4xx_it.o(i.SysTick_Handler)
    i.SystemInit                             0x080024dc   Section        0  system_gd32f4xx.o(i.SystemInit)
    i.USART0_IRQHandler                      0x08002658   Section        0  gd32f4xx_it.o(i.USART0_IRQHandler)
    i.UsageFault_Handler                     0x080026d0   Section        0  gd32f4xx_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x080026d4   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__NVIC_SetPriority                     0x08002704   Section        0  systick.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08002705   Thumb Code    32  systick.o(i.__NVIC_SetPriority)
    i.__perf_counter_init                    0x0800272c   Section        0  perf_counter.o(i.__perf_counter_init)
    i.__perf_os_patch_init                   0x08002736   Section        0  perf_counter.o(i.__perf_os_patch_init)
    i._is_digit                              0x08002738   Section        0  __printf_wp.o(i._is_digit)
    i._soft_delay_                           0x08002746   Section        0  system_gd32f4xx.o(i._soft_delay_)
    _soft_delay_                             0x08002747   Thumb Code    28  system_gd32f4xx.o(i._soft_delay_)
    i.adc_calibration_enable                 0x08002762   Section        0  gd32f4xx_adc.o(i.adc_calibration_enable)
    i.adc_channel_length_config              0x0800278c   Section        0  gd32f4xx_adc.o(i.adc_channel_length_config)
    i.adc_clock_config                       0x080027e0   Section        0  gd32f4xx_adc.o(i.adc_clock_config)
    i.adc_data_alignment_config              0x08002804   Section        0  gd32f4xx_adc.o(i.adc_data_alignment_config)
    i.adc_dma_mode_enable                    0x0800281a   Section        0  gd32f4xx_adc.o(i.adc_dma_mode_enable)
    i.adc_dma_request_after_last_enable      0x08002824   Section        0  gd32f4xx_adc.o(i.adc_dma_request_after_last_enable)
    i.adc_enable                             0x0800282e   Section        0  gd32f4xx_adc.o(i.adc_enable)
    i.adc_external_trigger_config            0x08002840   Section        0  gd32f4xx_adc.o(i.adc_external_trigger_config)
    i.adc_external_trigger_source_config     0x08002874   Section        0  gd32f4xx_adc.o(i.adc_external_trigger_source_config)
    i.adc_routine_channel_config             0x080028a4   Section        0  gd32f4xx_adc.o(i.adc_routine_channel_config)
    i.adc_software_trigger_enable            0x08002950   Section        0  gd32f4xx_adc.o(i.adc_software_trigger_enable)
    i.adc_special_function_config            0x08002974   Section        0  gd32f4xx_adc.o(i.adc_special_function_config)
    i.adc_sync_mode_config                   0x080029d0   Section        0  gd32f4xx_adc.o(i.adc_sync_mode_config)
    i.adc_task                               0x080029f4   Section        0  adc_app.o(i.adc_task)
    i.app_btn_init                           0x08002a08   Section        0  btn_app.o(i.app_btn_init)
    i.bit_array_and                          0x08002a2c   Section        0  ebtn.o(i.bit_array_and)
    bit_array_and                            0x08002a2d   Thumb Code    38  ebtn.o(i.bit_array_and)
    i.bit_array_assign                       0x08002a52   Section        0  ebtn.o(i.bit_array_assign)
    bit_array_assign                         0x08002a53   Thumb Code    46  ebtn.o(i.bit_array_assign)
    i.bit_array_cmp                          0x08002a80   Section        0  ebtn.o(i.bit_array_cmp)
    bit_array_cmp                            0x08002a81   Thumb Code    36  ebtn.o(i.bit_array_cmp)
    i.bit_array_get                          0x08002aa4   Section        0  ebtn.o(i.bit_array_get)
    bit_array_get                            0x08002aa5   Thumb Code    22  ebtn.o(i.bit_array_get)
    i.bit_array_num_bits_set                 0x08002aba   Section        0  ebtn.o(i.bit_array_num_bits_set)
    bit_array_num_bits_set                   0x08002abb   Thumb Code    84  ebtn.o(i.bit_array_num_bits_set)
    i.bit_array_or                           0x08002b0e   Section        0  ebtn.o(i.bit_array_or)
    bit_array_or                             0x08002b0f   Thumb Code    38  ebtn.o(i.bit_array_or)
    i.bsp_adc_init                           0x08002b34   Section        0  mcu_cmic_gd32f470vet6.o(i.bsp_adc_init)
    i.bsp_btn_init                           0x08002c50   Section        0  mcu_cmic_gd32f470vet6.o(i.bsp_btn_init)
    i.bsp_dac_init                           0x08002ca0   Section        0  mcu_cmic_gd32f470vet6.o(i.bsp_dac_init)
    i.bsp_gd25qxx_init                       0x08002d9c   Section        0  mcu_cmic_gd32f470vet6.o(i.bsp_gd25qxx_init)
    i.bsp_led_init                           0x08002e34   Section        0  mcu_cmic_gd32f470vet6.o(i.bsp_led_init)
    i.bsp_oled_init                          0x08002e6c   Section        0  mcu_cmic_gd32f470vet6.o(i.bsp_oled_init)
    i.bsp_rtc_init                           0x08002f64   Section        0  mcu_cmic_gd32f470vet6.o(i.bsp_rtc_init)
    i.bsp_rtc_pre_cfg                        0x08002f98   Section        0  mcu_cmic_gd32f470vet6.o(i.bsp_rtc_pre_cfg)
    i.bsp_rtc_setup                          0x08002fd4   Section        0  mcu_cmic_gd32f470vet6.o(i.bsp_rtc_setup)
    i.bsp_usart_init                         0x08003034   Section        0  mcu_cmic_gd32f470vet6.o(i.bsp_usart_init)
    i.btn_task                               0x08003154   Section        0  btn_app.o(i.btn_task)
    i.card_info_get                          0x08003164   Section        0  sd_app.o(i.card_info_get)
    i.check_fs                               0x0800352c   Section        0  ff.o(i.check_fs)
    check_fs                                 0x0800352d   Thumb Code   138  ff.o(i.check_fs)
    i.check_systick                          0x080035bc   Section        0  perf_counter.o(i.check_systick)
    check_systick                            0x080035bd   Thumb Code    48  perf_counter.o(i.check_systick)
    i.chk_chr                                0x080035ec   Section        0  ff.o(i.chk_chr)
    chk_chr                                  0x080035ed   Thumb Code    20  ff.o(i.chk_chr)
    i.chk_mounted                            0x08003600   Section        0  ff.o(i.chk_mounted)
    chk_mounted                              0x08003601   Thumb Code   898  ff.o(i.chk_mounted)
    i.clust2sect                             0x08003994   Section        0  ff.o(i.clust2sect)
    i.cmdsent_error_check                    0x080039b0   Section        0  sdio_sdcard.o(i.cmdsent_error_check)
    cmdsent_error_check                      0x080039b1   Thumb Code    40  sdio_sdcard.o(i.cmdsent_error_check)
    i.create_chain                           0x080039e0   Section        0  ff.o(i.create_chain)
    create_chain                             0x080039e1   Thumb Code   202  ff.o(i.create_chain)
    i.create_name                            0x08003aac   Section        0  ff.o(i.create_name)
    create_name                              0x08003aad   Thumb Code   336  ff.o(i.create_name)
    i.dac_deinit                             0x08003c0c   Section        0  gd32f4xx_dac.o(i.dac_deinit)
    i.dac_dma_enable                         0x08003c34   Section        0  gd32f4xx_dac.o(i.dac_dma_enable)
    i.dac_enable                             0x08003c4e   Section        0  gd32f4xx_dac.o(i.dac_enable)
    i.dac_trigger_enable                     0x08003c68   Section        0  gd32f4xx_dac.o(i.dac_trigger_enable)
    i.dac_trigger_source_config              0x08003c82   Section        0  gd32f4xx_dac.o(i.dac_trigger_source_config)
    i.dac_wave_mode_config                   0x08003caa   Section        0  gd32f4xx_dac.o(i.dac_wave_mode_config)
    i.delay_1ms                              0x08003cd4   Section        0  systick.o(i.delay_1ms)
    i.delay_decrement                        0x08003ce8   Section        0  systick.o(i.delay_decrement)
    i.delay_ms                               0x08003d00   Section        0  perf_counter.o(i.delay_ms)
    i.dir_find                               0x08003d54   Section        0  ff.o(i.dir_find)
    dir_find                                 0x08003d55   Thumb Code    92  ff.o(i.dir_find)
    i.dir_next                               0x08003db0   Section        0  ff.o(i.dir_next)
    dir_next                                 0x08003db1   Thumb Code   280  ff.o(i.dir_next)
    i.dir_register                           0x08003ec8   Section        0  ff.o(i.dir_register)
    dir_register                             0x08003ec9   Thumb Code   110  ff.o(i.dir_register)
    i.dir_sdi                                0x08003f36   Section        0  ff.o(i.dir_sdi)
    dir_sdi                                  0x08003f37   Thumb Code   156  ff.o(i.dir_sdi)
    i.disk_initialize                        0x08003fd2   Section        0  diskio.o(i.disk_initialize)
    i.disk_ioctl                             0x08004058   Section        0  diskio.o(i.disk_ioctl)
    i.disk_read                              0x0800405e   Section        0  diskio.o(i.disk_read)
    i.disk_status                            0x080040ae   Section        0  diskio.o(i.disk_status)
    i.disk_write                             0x080040ba   Section        0  diskio.o(i.disk_write)
    i.dma_channel_disable                    0x0800410a   Section        0  gd32f4xx_dma.o(i.dma_channel_disable)
    i.dma_channel_enable                     0x0800412a   Section        0  gd32f4xx_dma.o(i.dma_channel_enable)
    i.dma_channel_subperipheral_select       0x0800414a   Section        0  gd32f4xx_dma.o(i.dma_channel_subperipheral_select)
    i.dma_circulation_disable                0x08004170   Section        0  gd32f4xx_dma.o(i.dma_circulation_disable)
    i.dma_circulation_enable                 0x08004190   Section        0  gd32f4xx_dma.o(i.dma_circulation_enable)
    i.dma_deinit                             0x080041b0   Section        0  gd32f4xx_dma.o(i.dma_deinit)
    i.dma_flag_clear                         0x08004256   Section        0  gd32f4xx_dma.o(i.dma_flag_clear)
    i.dma_flag_get                           0x08004294   Section        0  gd32f4xx_dma.o(i.dma_flag_get)
    i.dma_flow_controller_config             0x080042e0   Section        0  gd32f4xx_dma.o(i.dma_flow_controller_config)
    i.dma_memory_address_config              0x08004320   Section        0  gd32f4xx_dma.o(i.dma_memory_address_config)
    i.dma_multi_data_mode_init               0x08004340   Section        0  gd32f4xx_dma.o(i.dma_multi_data_mode_init)
    i.dma_receive_config                     0x080044a4   Section        0  sdio_sdcard.o(i.dma_receive_config)
    dma_receive_config                       0x080044a5   Thumb Code   170  sdio_sdcard.o(i.dma_receive_config)
    i.dma_single_data_mode_init              0x08004558   Section        0  gd32f4xx_dma.o(i.dma_single_data_mode_init)
    i.dma_single_data_para_struct_init       0x080046b0   Section        0  gd32f4xx_dma.o(i.dma_single_data_para_struct_init)
    i.dma_transfer_config                    0x080046d4   Section        0  sdio_sdcard.o(i.dma_transfer_config)
    dma_transfer_config                      0x080046d5   Thumb Code   172  sdio_sdcard.o(i.dma_transfer_config)
    i.dma_transfer_number_config             0x08004788   Section        0  gd32f4xx_dma.o(i.dma_transfer_number_config)
    i.dma_transfer_number_get                0x08004798   Section        0  gd32f4xx_dma.o(i.dma_transfer_number_get)
    i.ebtn_get_current_state                 0x080047a8   Section        0  ebtn.o(i.ebtn_get_current_state)
    ebtn_get_current_state                   0x080047a9   Thumb Code    82  ebtn.o(i.ebtn_get_current_state)
    i.ebtn_init                              0x08004800   Section        0  ebtn.o(i.ebtn_init)
    i.ebtn_process                           0x0800485c   Section        0  ebtn.o(i.ebtn_process)
    i.ebtn_process_btn                       0x08004876   Section        0  ebtn.o(i.ebtn_process_btn)
    ebtn_process_btn                         0x08004877   Thumb Code    62  ebtn.o(i.ebtn_process_btn)
    i.ebtn_process_btn_combo                 0x080048b4   Section        0  ebtn.o(i.ebtn_process_btn_combo)
    ebtn_process_btn_combo                   0x080048b5   Thumb Code   124  ebtn.o(i.ebtn_process_btn_combo)
    i.ebtn_process_with_curr_state           0x08004930   Section        0  ebtn.o(i.ebtn_process_with_curr_state)
    i.ebtn_timer_sub                         0x08004af0   Section        0  ebtn.o(i.ebtn_timer_sub)
    ebtn_timer_sub                           0x08004af1   Thumb Code     6  ebtn.o(i.ebtn_timer_sub)
    i.f_close                                0x08004af6   Section        0  ff.o(i.f_close)
    i.f_mount                                0x08004b0c   Section        0  ff.o(i.f_mount)
    i.f_open                                 0x08004b38   Section        0  ff.o(i.f_open)
    i.f_read                                 0x08004ca4   Section        0  ff.o(i.f_read)
    i.f_sync                                 0x08004e72   Section        0  ff.o(i.f_sync)
    i.f_write                                0x08004f2a   Section        0  ff.o(i.f_write)
    i.follow_path                            0x08005138   Section        0  ff.o(i.follow_path)
    follow_path                              0x08005139   Thumb Code   158  ff.o(i.follow_path)
    i.gd32f4xx_firmware_version_get          0x080051d6   Section        0  system_gd32f4xx.o(i.gd32f4xx_firmware_version_get)
    i.get_fat                                0x080051dc   Section        0  ff.o(i.get_fat)
    i.get_fattime                            0x080052c0   Section        0  diskio.o(i.get_fattime)
    i.get_system_ms                          0x080052c4   Section        0  perf_counter.o(i.get_system_ms)
    i.get_system_ticks                       0x08005344   Section        0  perf_counter.o(i.get_system_ticks)
    __tagsym$$noinline                       0x08005345   Number         0  perf_counter.o(i.get_system_ticks)
    i.gpio_af_set                            0x080053a4   Section        0  gd32f4xx_gpio.o(i.gpio_af_set)
    i.gpio_bit_reset                         0x08005402   Section        0  gd32f4xx_gpio.o(i.gpio_bit_reset)
    i.gpio_bit_set                           0x08005406   Section        0  gd32f4xx_gpio.o(i.gpio_bit_set)
    i.gpio_config                            0x0800540c   Section        0  sdio_sdcard.o(i.gpio_config)
    gpio_config                              0x0800540d   Thumb Code   106  sdio_sdcard.o(i.gpio_config)
    i.gpio_input_bit_get                     0x08005480   Section        0  gd32f4xx_gpio.o(i.gpio_input_bit_get)
    i.gpio_mode_set                          0x08005490   Section        0  gd32f4xx_gpio.o(i.gpio_mode_set)
    i.gpio_output_options_set                0x080054de   Section        0  gd32f4xx_gpio.o(i.gpio_output_options_set)
    i.i2c_ack_config                         0x08005520   Section        0  gd32f4xx_i2c.o(i.i2c_ack_config)
    i.i2c_clock_config                       0x08005530   Section        0  gd32f4xx_i2c.o(i.i2c_clock_config)
    i.i2c_deinit                             0x08005614   Section        0  gd32f4xx_i2c.o(i.i2c_deinit)
    i.i2c_dma_config                         0x0800566c   Section        0  gd32f4xx_i2c.o(i.i2c_dma_config)
    i.i2c_enable                             0x0800567c   Section        0  gd32f4xx_i2c.o(i.i2c_enable)
    i.i2c_flag_clear                         0x08005686   Section        0  gd32f4xx_i2c.o(i.i2c_flag_clear)
    i.i2c_flag_get                           0x080056ae   Section        0  gd32f4xx_i2c.o(i.i2c_flag_get)
    i.i2c_master_addressing                  0x080056cc   Section        0  gd32f4xx_i2c.o(i.i2c_master_addressing)
    i.i2c_mode_addr_config                   0x080056e0   Section        0  gd32f4xx_i2c.o(i.i2c_mode_addr_config)
    i.i2c_start_on_bus                       0x080056fc   Section        0  gd32f4xx_i2c.o(i.i2c_start_on_bus)
    i.i2c_stop_on_bus                        0x08005706   Section        0  gd32f4xx_i2c.o(i.i2c_stop_on_bus)
    i.init_cycle_counter                     0x08005710   Section        0  perf_counter.o(i.init_cycle_counter)
    i.led_disp                               0x0800578c   Section        0  led_app.o(i.led_disp)
    i.led_task                               0x08005870   Section        0  led_app.o(i.led_task)
    i.main                                   0x08005880   Section        0  main.o(i.main)
    i.mem_cmp                                0x080058d6   Section        0  ff.o(i.mem_cmp)
    mem_cmp                                  0x080058d7   Thumb Code    38  ff.o(i.mem_cmp)
    i.mem_cpy                                0x080058fc   Section        0  ff.o(i.mem_cpy)
    mem_cpy                                  0x080058fd   Thumb Code    26  ff.o(i.mem_cpy)
    i.mem_set                                0x08005916   Section        0  ff.o(i.mem_set)
    mem_set                                  0x08005917   Thumb Code    20  ff.o(i.mem_set)
    i.memory_compare                         0x0800592a   Section        0  sd_app.o(i.memory_compare)
    i.move_window                            0x0800594e   Section        0  ff.o(i.move_window)
    move_window                              0x0800594f   Thumb Code   114  ff.o(i.move_window)
    i.my_printf                              0x080059c0   Section        0  usart_app.o(i.my_printf)
    i.nvic_irq_enable                        0x08005a28   Section        0  gd32f4xx_misc.o(i.nvic_irq_enable)
    i.nvic_priority_group_set                0x08005aec   Section        0  gd32f4xx_misc.o(i.nvic_priority_group_set)
    i.oled_printf                            0x08005b00   Section        0  oled_app.o(i.oled_printf)
    i.oled_task                              0x08005b3c   Section        0  oled_app.o(i.oled_task)
    i.perfc_port_clear_system_timer_ovf_pending 0x08005c00   Section        0  perfc_port_default.o(i.perfc_port_clear_system_timer_ovf_pending)
    i.perfc_port_disable_global_interrupt    0x08005c10   Section        0  perf_counter.o(i.perfc_port_disable_global_interrupt)
    perfc_port_disable_global_interrupt      0x08005c11   Thumb Code    12  perf_counter.o(i.perfc_port_disable_global_interrupt)
    i.perfc_port_get_system_timer_elapsed    0x08005c1c   Section        0  perfc_port_default.o(i.perfc_port_get_system_timer_elapsed)
    i.perfc_port_get_system_timer_freq       0x08005c30   Section        0  perfc_port_default.o(i.perfc_port_get_system_timer_freq)
    i.perfc_port_get_system_timer_top        0x08005c3c   Section        0  perfc_port_default.o(i.perfc_port_get_system_timer_top)
    i.perfc_port_init_system_timer           0x08005c46   Section        0  perfc_port_default.o(i.perfc_port_init_system_timer)
    i.perfc_port_insert_to_system_timer_insert_ovf_handler 0x08005c98   Section        0  perf_counter.o(i.perfc_port_insert_to_system_timer_insert_ovf_handler)
    i.perfc_port_is_system_timer_ovf_pending 0x08005d94   Section        0  perfc_port_default.o(i.perfc_port_is_system_timer_ovf_pending)
    i.perfc_port_resume_global_interrupt     0x08005da4   Section        0  perf_counter.o(i.perfc_port_resume_global_interrupt)
    perfc_port_resume_global_interrupt       0x08005da5   Thumb Code    10  perf_counter.o(i.perfc_port_resume_global_interrupt)
    i.pmu_backup_write_enable                0x08005db0   Section        0  gd32f4xx_pmu.o(i.pmu_backup_write_enable)
    i.prv_btn_event                          0x08005dc4   Section        0  btn_app.o(i.prv_btn_event)
    i.prv_btn_get_state                      0x08005e48   Section        0  btn_app.o(i.prv_btn_get_state)
    i.prv_get_combo_btn_by_key_id            0x08005ef4   Section        0  ebtn.o(i.prv_get_combo_btn_by_key_id)
    prv_get_combo_btn_by_key_id              0x08005ef5   Thumb Code    70  ebtn.o(i.prv_get_combo_btn_by_key_id)
    i.prv_process_btn                        0x08005f40   Section        0  ebtn.o(i.prv_process_btn)
    prv_process_btn                          0x08005f41   Thumb Code   878  ebtn.o(i.prv_process_btn)
    i.put_fat                                0x080062b4   Section        0  ff.o(i.put_fat)
    i.r1_error_check                         0x080063ec   Section        0  sdio_sdcard.o(i.r1_error_check)
    r1_error_check                           0x080063ed   Thumb Code   120  sdio_sdcard.o(i.r1_error_check)
    i.r1_error_type_check                    0x08006470   Section        0  sdio_sdcard.o(i.r1_error_type_check)
    r1_error_type_check                      0x08006471   Thumb Code   174  sdio_sdcard.o(i.r1_error_type_check)
    i.r2_error_check                         0x08006520   Section        0  sdio_sdcard.o(i.r2_error_check)
    r2_error_check                           0x08006521   Thumb Code    70  sdio_sdcard.o(i.r2_error_check)
    i.r3_error_check                         0x08006570   Section        0  sdio_sdcard.o(i.r3_error_check)
    r3_error_check                           0x08006571   Thumb Code    52  sdio_sdcard.o(i.r3_error_check)
    i.r6_error_check                         0x080065ac   Section        0  sdio_sdcard.o(i.r6_error_check)
    r6_error_check                           0x080065ad   Thumb Code   158  sdio_sdcard.o(i.r6_error_check)
    i.r7_error_check                         0x08006654   Section        0  sdio_sdcard.o(i.r7_error_check)
    r7_error_check                           0x08006655   Thumb Code    74  sdio_sdcard.o(i.r7_error_check)
    i.rcu_all_reset_flag_clear               0x080066a4   Section        0  gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear)
    i.rcu_clock_freq_get                     0x080066b8   Section        0  gd32f4xx_rcu.o(i.rcu_clock_freq_get)
    i.rcu_config                             0x080067dc   Section        0  sdio_sdcard.o(i.rcu_config)
    rcu_config                               0x080067dd   Thumb Code    36  sdio_sdcard.o(i.rcu_config)
    i.rcu_flag_get                           0x08006800   Section        0  gd32f4xx_rcu.o(i.rcu_flag_get)
    i.rcu_osci_on                            0x08006824   Section        0  gd32f4xx_rcu.o(i.rcu_osci_on)
    i.rcu_osci_stab_wait                     0x08006848   Section        0  gd32f4xx_rcu.o(i.rcu_osci_stab_wait)
    i.rcu_periph_clock_enable                0x080069a4   Section        0  gd32f4xx_rcu.o(i.rcu_periph_clock_enable)
    i.rcu_periph_reset_disable               0x080069c8   Section        0  gd32f4xx_rcu.o(i.rcu_periph_reset_disable)
    i.rcu_periph_reset_enable                0x080069ec   Section        0  gd32f4xx_rcu.o(i.rcu_periph_reset_enable)
    i.rcu_rtc_clock_config                   0x08006a10   Section        0  gd32f4xx_rcu.o(i.rcu_rtc_clock_config)
    i.remove_chain                           0x08006a28   Section        0  ff.o(i.remove_chain)
    remove_chain                             0x08006a29   Thumb Code   104  ff.o(i.remove_chain)
    i.rtc_current_time_get                   0x08006a90   Section        0  gd32f4xx_rtc.o(i.rtc_current_time_get)
    i.rtc_init                               0x08006af4   Section        0  gd32f4xx_rtc.o(i.rtc_init)
    i.rtc_init_mode_enter                    0x08006bb8   Section        0  gd32f4xx_rtc.o(i.rtc_init_mode_enter)
    i.rtc_init_mode_exit                     0x08006c00   Section        0  gd32f4xx_rtc.o(i.rtc_init_mode_exit)
    i.rtc_register_sync_wait                 0x08006c14   Section        0  gd32f4xx_rtc.o(i.rtc_register_sync_wait)
    i.rtc_task                               0x08006c74   Section        0  rtc_app.o(i.rtc_task)
    i.scheduler_init                         0x08006cb0   Section        0  scheduler.o(i.scheduler_init)
    i.scheduler_run                          0x08006cbc   Section        0  scheduler.o(i.scheduler_run)
    i.sd_block_read                          0x08006d14   Section        0  sdio_sdcard.o(i.sd_block_read)
    i.sd_block_write                         0x08006f2c   Section        0  sdio_sdcard.o(i.sd_block_write)
    i.sd_bus_mode_config                     0x0800724c   Section        0  sdio_sdcard.o(i.sd_bus_mode_config)
    i.sd_bus_width_config                    0x080072e0   Section        0  sdio_sdcard.o(i.sd_bus_width_config)
    sd_bus_width_config                      0x080072e1   Thumb Code   242  sdio_sdcard.o(i.sd_bus_width_config)
    i.sd_card_capacity_get                   0x080073dc   Section        0  sdio_sdcard.o(i.sd_card_capacity_get)
    i.sd_card_information_get                0x08007484   Section        0  sdio_sdcard.o(i.sd_card_information_get)
    i.sd_card_init                           0x08007744   Section        0  sdio_sdcard.o(i.sd_card_init)
    i.sd_card_select_deselect                0x08007860   Section        0  sdio_sdcard.o(i.sd_card_select_deselect)
    i.sd_card_state_get                      0x08007888   Section        0  sdio_sdcard.o(i.sd_card_state_get)
    sd_card_state_get                        0x08007889   Thumb Code   166  sdio_sdcard.o(i.sd_card_state_get)
    i.sd_cardstatus_get                      0x08007940   Section        0  sdio_sdcard.o(i.sd_cardstatus_get)
    i.sd_datablocksize_get                   0x08007988   Section        0  sdio_sdcard.o(i.sd_datablocksize_get)
    sd_datablocksize_get                     0x08007989   Thumb Code    24  sdio_sdcard.o(i.sd_datablocksize_get)
    i.sd_fatfs_init                          0x080079a0   Section        0  sd_app.o(i.sd_fatfs_init)
    i.sd_fatfs_test                          0x080079b0   Section        0  sd_app.o(i.sd_fatfs_test)
    i.sd_init                                0x08007bdc   Section        0  sdio_sdcard.o(i.sd_init)
    i.sd_interrupts_process                  0x08007c24   Section        0  sdio_sdcard.o(i.sd_interrupts_process)
    i.sd_multiblocks_read                    0x08007d54   Section        0  sdio_sdcard.o(i.sd_multiblocks_read)
    i.sd_multiblocks_write                   0x08007ff0   Section        0  sdio_sdcard.o(i.sd_multiblocks_write)
    i.sd_power_on                            0x08008388   Section        0  sdio_sdcard.o(i.sd_power_on)
    i.sd_scr_get                             0x080084b4   Section        0  sdio_sdcard.o(i.sd_scr_get)
    sd_scr_get                               0x080084b5   Thumb Code   344  sdio_sdcard.o(i.sd_scr_get)
    i.sd_transfer_mode_config                0x08008610   Section        0  sdio_sdcard.o(i.sd_transfer_mode_config)
    i.sd_transfer_stop                       0x08008628   Section        0  sdio_sdcard.o(i.sd_transfer_stop)
    i.sdio_bus_mode_set                      0x0800864c   Section        0  gd32f4xx_sdio.o(i.sdio_bus_mode_set)
    i.sdio_clock_config                      0x08008668   Section        0  gd32f4xx_sdio.o(i.sdio_clock_config)
    i.sdio_clock_enable                      0x0800869c   Section        0  gd32f4xx_sdio.o(i.sdio_clock_enable)
    i.sdio_command_index_get                 0x080086b0   Section        0  gd32f4xx_sdio.o(i.sdio_command_index_get)
    i.sdio_command_response_config           0x080086bc   Section        0  gd32f4xx_sdio.o(i.sdio_command_response_config)
    i.sdio_csm_enable                        0x080086f4   Section        0  gd32f4xx_sdio.o(i.sdio_csm_enable)
    i.sdio_data_config                       0x08008708   Section        0  gd32f4xx_sdio.o(i.sdio_data_config)
    i.sdio_data_read                         0x08008744   Section        0  gd32f4xx_sdio.o(i.sdio_data_read)
    i.sdio_data_transfer_config              0x08008750   Section        0  gd32f4xx_sdio.o(i.sdio_data_transfer_config)
    i.sdio_data_write                        0x0800876c   Section        0  gd32f4xx_sdio.o(i.sdio_data_write)
    i.sdio_deinit                            0x08008778   Section        0  gd32f4xx_sdio.o(i.sdio_deinit)
    i.sdio_dma_disable                       0x0800878c   Section        0  gd32f4xx_sdio.o(i.sdio_dma_disable)
    i.sdio_dma_enable                        0x080087a0   Section        0  gd32f4xx_sdio.o(i.sdio_dma_enable)
    i.sdio_dsm_disable                       0x080087b4   Section        0  gd32f4xx_sdio.o(i.sdio_dsm_disable)
    i.sdio_dsm_enable                        0x080087c8   Section        0  gd32f4xx_sdio.o(i.sdio_dsm_enable)
    i.sdio_flag_clear                        0x080087dc   Section        0  gd32f4xx_sdio.o(i.sdio_flag_clear)
    i.sdio_flag_get                          0x080087e8   Section        0  gd32f4xx_sdio.o(i.sdio_flag_get)
    i.sdio_hardware_clock_disable            0x080087fc   Section        0  gd32f4xx_sdio.o(i.sdio_hardware_clock_disable)
    i.sdio_interrupt_disable                 0x08008810   Section        0  gd32f4xx_sdio.o(i.sdio_interrupt_disable)
    i.sdio_interrupt_enable                  0x08008820   Section        0  gd32f4xx_sdio.o(i.sdio_interrupt_enable)
    i.sdio_interrupt_flag_clear              0x08008830   Section        0  gd32f4xx_sdio.o(i.sdio_interrupt_flag_clear)
    i.sdio_interrupt_flag_get                0x0800883c   Section        0  gd32f4xx_sdio.o(i.sdio_interrupt_flag_get)
    i.sdio_power_state_get                   0x08008850   Section        0  gd32f4xx_sdio.o(i.sdio_power_state_get)
    i.sdio_power_state_set                   0x0800885c   Section        0  gd32f4xx_sdio.o(i.sdio_power_state_set)
    i.sdio_response_get                      0x08008868   Section        0  gd32f4xx_sdio.o(i.sdio_response_get)
    i.sdio_wait_type_set                     0x080088a4   Section        0  gd32f4xx_sdio.o(i.sdio_wait_type_set)
    i.spi_dma_disable                        0x080088c0   Section        0  gd32f4xx_spi.o(i.spi_dma_disable)
    i.spi_dma_enable                         0x080088d6   Section        0  gd32f4xx_spi.o(i.spi_dma_enable)
    i.spi_enable                             0x080088ec   Section        0  gd32f4xx_spi.o(i.spi_enable)
    i.spi_flash_buffer_read                  0x080088f8   Section        0  gd25qxx.o(i.spi_flash_buffer_read)
    i.spi_flash_init                         0x0800894c   Section        0  gd25qxx.o(i.spi_flash_init)
    i.spi_flash_read_id                      0x08008968   Section        0  gd25qxx.o(i.spi_flash_read_id)
    i.spi_flash_send_byte_dma                0x080089bc   Section        0  gd25qxx.o(i.spi_flash_send_byte_dma)
    i.spi_init                               0x08008ab8   Section        0  gd32f4xx_spi.o(i.spi_init)
    i.sync                                   0x08008aea   Section        0  ff.o(i.sync)
    sync                                     0x08008aeb   Thumb Code   202  ff.o(i.sync)
    i.system_clock_240m_25m_hxtal            0x08008bb4   Section        0  system_gd32f4xx.o(i.system_clock_240m_25m_hxtal)
    system_clock_240m_25m_hxtal              0x08008bb5   Thumb Code   258  system_gd32f4xx.o(i.system_clock_240m_25m_hxtal)
    i.system_clock_config                    0x08008cc4   Section        0  system_gd32f4xx.o(i.system_clock_config)
    system_clock_config                      0x08008cc5   Thumb Code     8  system_gd32f4xx.o(i.system_clock_config)
    i.systick_config                         0x08008ccc   Section        0  systick.o(i.systick_config)
    i.test_spi_flash                         0x08008d1c   Section        0  gd25qxx.o(i.test_spi_flash)
    i.timer5_config                          0x08009008   Section        0  mcu_cmic_gd32f470vet6.o(i.timer5_config)
    i.timer_deinit                           0x08009050   Section        0  gd32f4xx_timer.o(i.timer_deinit)
    i.timer_enable                           0x080091d4   Section        0  gd32f4xx_timer.o(i.timer_enable)
    i.timer_init                             0x080091e0   Section        0  gd32f4xx_timer.o(i.timer_init)
    i.timer_master_output_trigger_source_select 0x08009278   Section        0  gd32f4xx_timer.o(i.timer_master_output_trigger_source_select)
    i.timer_struct_para_init                 0x08009288   Section        0  gd32f4xx_timer.o(i.timer_struct_para_init)
    i.uart_task                              0x080092a0   Section        0  usart_app.o(i.uart_task)
    i.update_perf_counter                    0x080092d4   Section        0  perf_counter.o(i.update_perf_counter)
    i.usart_baudrate_set                     0x08009348   Section        0  gd32f4xx_usart.o(i.usart_baudrate_set)
    i.usart_data_receive                     0x08009430   Section        0  gd32f4xx_usart.o(i.usart_data_receive)
    i.usart_data_transmit                    0x0800943a   Section        0  gd32f4xx_usart.o(i.usart_data_transmit)
    i.usart_deinit                           0x08009444   Section        0  gd32f4xx_usart.o(i.usart_deinit)
    i.usart_dma_receive_config               0x08009520   Section        0  gd32f4xx_usart.o(i.usart_dma_receive_config)
    i.usart_enable                           0x08009534   Section        0  gd32f4xx_usart.o(i.usart_enable)
    i.usart_flag_get                         0x0800953e   Section        0  gd32f4xx_usart.o(i.usart_flag_get)
    i.usart_interrupt_enable                 0x0800955c   Section        0  gd32f4xx_usart.o(i.usart_interrupt_enable)
    i.usart_interrupt_flag_get               0x08009576   Section        0  gd32f4xx_usart.o(i.usart_interrupt_flag_get)
    i.usart_receive_config                   0x080095ae   Section        0  gd32f4xx_usart.o(i.usart_receive_config)
    i.usart_transmit_config                  0x080095be   Section        0  gd32f4xx_usart.o(i.usart_transmit_config)
    i.validate                               0x080095ce   Section        0  ff.o(i.validate)
    validate                                 0x080095cf   Thumb Code    42  ff.o(i.validate)
    locale$$code                             0x080095f8   Section       44  lc_numeric_c.o(locale$$code)
    locale$$code                             0x08009624   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$dretinf                            0x08009650   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x08009650   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$f2d                                0x0800965c   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x0800965c   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x080096b2   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x080096b2   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x0800973e   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x0800973e   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$printf1                            0x08009748   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x08009748   Number         0  printf1.o(x$fpl$printf1)
    x$fpl$printf2                            0x0800974c   Section        4  printf2.o(x$fpl$printf2)
    $v0                                      0x0800974c   Number         0  printf2.o(x$fpl$printf2)
    .constdata                               0x08009750   Section     2712  oled.o(.constdata)
    x$fpl$usenofp                            0x08009750   Section        0  usenofp.o(x$fpl$usenofp)
    F8X16                                    0x08009978   Data        1520  oled.o(.constdata)
    .constdata                               0x0800a1e8   Section       14  btn_app.o(.constdata)
    defaul_ebtn_param                        0x0800a1e8   Data          14  btn_app.o(.constdata)
    .constdata                               0x0800a1f6   Section       40  _printf_hex_int_ll_ptr.o(.constdata)
    uc_hextab                                0x0800a1f6   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    lc_hextab                                0x0800a20a   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    .constdata                               0x0800a21e   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x0800a21e   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x0800a230   Section        8  _printf_wctomb.o(.constdata)
    initial_mbstate                          0x0800a230   Data           8  _printf_wctomb.o(.constdata)
    .constdata                               0x0800a238   Section       38  _printf_fp_hex.o(.constdata)
    lc_hextab                                0x0800a238   Data          19  _printf_fp_hex.o(.constdata)
    uc_hextab                                0x0800a24b   Data          19  _printf_fp_hex.o(.constdata)
    .constdata                               0x0800a260   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x0800a260   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x0800a29c   Data          64  bigflt0.o(.constdata)
    locale$$data                             0x0800a314   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x0800a318   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x0800a320   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x0800a32c   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x0800a32e   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x0800a32f   Data           0  lc_numeric_c.o(locale$$data)
    locale$$data                             0x0800a330   Section      272  lc_ctype_c.o(locale$$data)
    __lcnum_c_end                            0x0800a330   Data           0  lc_numeric_c.o(locale$$data)
    __lcctype_c_name                         0x0800a334   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x0800a33c   Data           0  lc_ctype_c.o(locale$$data)
    .init_array                              0x0800a440   Section        4  perf_counter.o(.init_array)
    __lcctype_c_end                          0x0800a440   Data           0  lc_ctype_c.o(locale$$data)
    .init_array                              0x0800a444   Section        0  init_aeabi.o(.init_array)
    .data                                    0x20000000   Section        4  systick.o(.data)
    delay                                    0x20000000   Data           4  systick.o(.data)
    .data                                    0x20000004   Section       20  mcu_cmic_gd32f470vet6.o(.data)
    .data                                    0x20000018   Section       22  oled.o(.data)
    .data                                    0x20000030   Section       36  sdio_sdcard.o(.data)
    cardtype                                 0x20000038   Data           1  sdio_sdcard.o(.data)
    sd_rca                                   0x2000003a   Data           2  sdio_sdcard.o(.data)
    transmode                                0x2000003c   Data           4  sdio_sdcard.o(.data)
    totalnumber_bytes                        0x20000040   Data           4  sdio_sdcard.o(.data)
    stopcondition                            0x20000044   Data           4  sdio_sdcard.o(.data)
    transerror                               0x20000048   Data           1  sdio_sdcard.o(.data)
    transend                                 0x2000004c   Data           4  sdio_sdcard.o(.data)
    number_bytes                             0x20000050   Data           4  sdio_sdcard.o(.data)
    .data                                    0x20000054   Section        6  ff.o(.data)
    FatFs                                    0x20000054   Data           4  ff.o(.data)
    Fsid                                     0x20000058   Data           2  ff.o(.data)
    .data                                    0x2000005c   Section      196  btn_app.o(.data)
    btns                                     0x2000005c   Data         196  btn_app.o(.data)
    .data                                    0x20000120   Section        7  led_app.o(.data)
    temp_old                                 0x20000126   Data           1  led_app.o(.data)
    .data                                    0x20000128   Section       76  scheduler.o(.data)
    scheduler_task                           0x2000012c   Data          72  scheduler.o(.data)
    .data                                    0x20000174   Section        3  usart_app.o(.data)
    .data                                    0x20000178   Section       16  sd_app.o(.data)
    .data                                    0x20000188   Section        4  system_gd32f4xx.o(.data)
    .data                                    0x20000190   Section       80  perf_counter.o(.data)
    s_lOldTimestamp                          0x20000190   Data           8  perf_counter.o(.data)
    s_lOldTimestampUS                        0x20000198   Data           8  perf_counter.o(.data)
    s_lOldTimestampMS                        0x200001a0   Data           8  perf_counter.o(.data)
    s_wUSUnit                                0x200001a8   Data           4  perf_counter.o(.data)
    s_wMSUnit                                0x200001ac   Data           4  perf_counter.o(.data)
    s_wMSResidule                            0x200001b0   Data           4  perf_counter.o(.data)
    s_wUSResidule                            0x200001b4   Data           4  perf_counter.o(.data)
    s_lSystemMS                              0x200001b8   Data           8  perf_counter.o(.data)
    s_lSystemUS                              0x200001c0   Data           8  perf_counter.o(.data)
    s_lSystemClockCounts                     0x200001c8   Data           8  perf_counter.o(.data)
    .bss                                     0x200001e0   Section      572  mcu_cmic_gd32f470vet6.o(.bss)
    .bss                                     0x2000041c   Section       60  ebtn.o(.bss)
    ebtn_default                             0x2000041c   Data          60  ebtn.o(.bss)
    .bss                                     0x20000458   Section       32  sdio_sdcard.o(.bss)
    sd_csd                                   0x20000458   Data          16  sdio_sdcard.o(.bss)
    sd_cid                                   0x20000468   Data          16  sdio_sdcard.o(.bss)
    .bss                                     0x20000478   Section      512  usart_app.o(.bss)
    .bss                                     0x20000678   Section     1436  sd_app.o(.bss)
    .bss                                     0x20000c14   Section       96  libspace.o(.bss)
    HEAP                                     0x20000c78   Section     1024  startup_gd32f450_470.o(HEAP)
    Heap_Mem                                 0x20000c78   Data        1024  startup_gd32f450_470.o(HEAP)
    STACK                                    0x20001078   Section     1024  startup_gd32f450_470.o(STACK)
    Stack_Mem                                0x20001078   Data        1024  startup_gd32f450_470.o(STACK)
    __initial_sp                             0x20001478   Data           0  startup_gd32f450_470.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    __user_heap_extent                        - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_free                               - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    __Vectors_Size                           0x000001ac   Number         0  startup_gd32f450_470.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_gd32f450_470.o(RESET)
    __Vectors_End                            0x080001ac   Data           0  startup_gd32f450_470.o(RESET)
    __main                                   0x080001ad   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080001b5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080001b5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080001b5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x080001c3   Thumb Code     0  __scatter.o(!!!scatter)
    __decompress                             0x080001e9   Thumb Code    90  __dczerorl2.o(!!dczerorl2)
    __decompress1                            0x080001e9   Thumb Code     0  __dczerorl2.o(!!dczerorl2)
    __scatterload_zeroinit                   0x08000245   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_n                                0x08000261   Thumb Code     0  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    _printf_percent                          0x08000261   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_p                                0x08000267   Thumb Code     0  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    _printf_f                                0x0800026d   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_e                                0x08000273   Thumb Code     0  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    _printf_g                                0x08000279   Thumb Code     0  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    _printf_a                                0x0800027f   Thumb Code     0  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    _printf_ll                               0x08000285   Thumb Code     0  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    _printf_i                                0x0800028f   Thumb Code     0  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    _printf_d                                0x08000295   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x0800029b   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_o                                0x080002a1   Thumb Code     0  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    _printf_x                                0x080002a7   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_lli                              0x080002ad   Thumb Code     0  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    _printf_lld                              0x080002b3   Thumb Code     0  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    _printf_llu                              0x080002b9   Thumb Code     0  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    _printf_llo                              0x080002bf   Thumb Code     0  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    _printf_llx                              0x080002c5   Thumb Code     0  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    _printf_l                                0x080002cb   Thumb Code     0  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    _printf_c                                0x080002d5   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x080002db   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_lc                               0x080002e1   Thumb Code     0  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    _printf_ls                               0x080002e7   Thumb Code     0  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    _printf_percent_end                      0x080002ed   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x080002f1   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080002f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x080002f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x080002f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x080002f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080002f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x080002f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x080002fd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x080002fd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_lc_ctype_1                 0x08000309   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000309   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x08000309   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_2                      0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000031)
    __rt_lib_init_exceptions_1               0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_signal_1                   0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_cpp_1                      0x08000317   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_return                     0x08000317   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_shutdown                        0x08000319   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x0800031b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x0800031b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x0800031b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x0800031b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x0800031b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x0800031b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x0800031b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x0800031b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x0800031d   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x0800031d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x0800031d   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000323   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000323   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000327   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000327   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800032f   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000331   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000331   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000335   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x0800033d   Thumb Code     8  startup_gd32f450_470.o(.text)
    ADC_IRQHandler                           0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_EWMC_IRQHandler                     0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_RX0_IRQHandler                      0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_RX1_IRQHandler                      0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_TX_IRQHandler                       0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_EWMC_IRQHandler                     0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_RX0_IRQHandler                      0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_RX1_IRQHandler                      0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_TX_IRQHandler                       0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    DCI_IRQHandler                           0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel0_IRQHandler                 0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel1_IRQHandler                 0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel2_IRQHandler                 0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel3_IRQHandler                 0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel4_IRQHandler                 0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel5_IRQHandler                 0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel6_IRQHandler                 0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel7_IRQHandler                 0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel0_IRQHandler                 0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel1_IRQHandler                 0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel2_IRQHandler                 0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel3_IRQHandler                 0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel4_IRQHandler                 0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel5_IRQHandler                 0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel6_IRQHandler                 0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel7_IRQHandler                 0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    ENET_IRQHandler                          0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    ENET_WKUP_IRQHandler                     0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXMC_IRQHandler                          0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI0_IRQHandler                         0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI10_15_IRQHandler                     0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI1_IRQHandler                         0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI2_IRQHandler                         0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI3_IRQHandler                         0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI4_IRQHandler                         0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI5_9_IRQHandler                       0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    FMC_IRQHandler                           0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    FPU_IRQHandler                           0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C0_ER_IRQHandler                       0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C0_EV_IRQHandler                       0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C1_ER_IRQHandler                       0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C1_EV_IRQHandler                       0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C2_ER_IRQHandler                       0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C2_EV_IRQHandler                       0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    IPA_IRQHandler                           0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    LVD_IRQHandler                           0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    RCU_CTC_IRQHandler                       0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    RTC_Alarm_IRQHandler                     0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    RTC_WKUP_IRQHandler                      0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI0_IRQHandler                          0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI1_IRQHandler                          0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI2_IRQHandler                          0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI3_IRQHandler                          0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI4_IRQHandler                          0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI5_IRQHandler                          0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    TAMPER_STAMP_IRQHandler                  0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_BRK_TIMER8_IRQHandler             0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_Channel_IRQHandler                0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_TRG_CMT_TIMER10_IRQHandler        0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_UP_TIMER9_IRQHandler              0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER1_IRQHandler                        0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER2_IRQHandler                        0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER3_IRQHandler                        0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER4_IRQHandler                        0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER5_DAC_IRQHandler                    0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER6_IRQHandler                        0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_BRK_TIMER11_IRQHandler            0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_Channel_IRQHandler                0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_TRG_CMT_TIMER13_IRQHandler        0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_UP_TIMER12_IRQHandler             0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    TLI_ER_IRQHandler                        0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    TLI_IRQHandler                           0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    TRNG_IRQHandler                          0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART3_IRQHandler                         0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART4_IRQHandler                         0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART6_IRQHandler                         0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART7_IRQHandler                         0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART1_IRQHandler                        0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART2_IRQHandler                        0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART5_IRQHandler                        0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBFS_IRQHandler                         0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBFS_WKUP_IRQHandler                    0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_EP1_In_IRQHandler                  0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_EP1_Out_IRQHandler                 0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_IRQHandler                         0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_WKUP_IRQHandler                    0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    WWDGT_IRQHandler                         0x08000357   Thumb Code     0  startup_gd32f450_470.o(.text)
    __user_initial_stackheap                 0x08000359   Thumb Code    10  startup_gd32f450_470.o(.text)
    SysTick_Handler                          0x0800037d   Thumb Code    18  systick_wrapper_ual.o(.text)
    __ensure_systick_wrapper                 0x08000399   Thumb Code     4  systick_wrapper_ual.o(.text)
    __aeabi_ldivmod                          0x0800039d   Thumb Code     0  llsdiv.o(.text)
    _ll_sdiv                                 0x0800039d   Thumb Code    72  llsdiv.o(.text)
    vsnprintf                                0x080003e5   Thumb Code    48  vsnprintf.o(.text)
    __2sprintf                               0x08000419   Thumb Code    38  __2sprintf.o(.text)
    _printf_int_dec                          0x08000445   Thumb Code   104  _printf_dec.o(.text)
    _printf_longlong_hex                     0x080004bd   Thumb Code    86  _printf_hex_int_ll_ptr.o(.text)
    _printf_int_hex                          0x08000513   Thumb Code    28  _printf_hex_int_ll_ptr.o(.text)
    _printf_ll_hex                           0x0800052f   Thumb Code    12  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_ptr                          0x0800053b   Thumb Code    18  _printf_hex_int_ll_ptr.o(.text)
    __printf                                 0x08000551   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    memcmp                                   0x080006d9   Thumb Code    88  memcmp.o(.text)
    strlen                                   0x08000731   Thumb Code    62  strlen.o(.text)
    __aeabi_memcpy                           0x0800076f   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x0800076f   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x080007d5   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memcpy4                          0x080007f9   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x080007f9   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x080007f9   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x08000841   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memclr                           0x0800085d   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x0800085d   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x08000861   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr4                          0x080008a1   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x080008a1   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x080008a1   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x080008a5   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x080008ef   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow                         0x080008f1   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand                         0x080008f3   Thumb Code     2  heapauxi.o(.text)
    __cpp_initialize__aeabi_                 0x080008f5   Thumb Code    26  init_aeabi.o(.text)
    __aeabi_uldivmod                         0x08000919   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x08000919   Thumb Code   238  lludivv7m.o(.text)
    _printf_pre_padding                      0x08000a07   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x08000a33   Thumb Code    34  _printf_pad.o(.text)
    _printf_truncate_signed                  0x08000a55   Thumb Code    18  _printf_truncate.o(.text)
    _printf_truncate_unsigned                0x08000a67   Thumb Code    18  _printf_truncate.o(.text)
    _printf_str                              0x08000a79   Thumb Code    82  _printf_str.o(.text)
    _printf_int_common                       0x08000acb   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_charcount                        0x08000b7d   Thumb Code    40  _printf_charcount.o(.text)
    _printf_char_common                      0x08000baf   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000bd5   Thumb Code    10  _sputc.o(.text)
    _snputc                                  0x08000bdf   Thumb Code    16  _snputc.o(.text)
    _printf_wctomb                           0x08000bf1   Thumb Code   182  _printf_wctomb.o(.text)
    _printf_longlong_dec                     0x08000cad   Thumb Code   108  _printf_longlong_dec.o(.text)
    _printf_longlong_oct                     0x08000d29   Thumb Code    66  _printf_oct_int_ll.o(.text)
    _printf_int_oct                          0x08000d6b   Thumb Code    24  _printf_oct_int_ll.o(.text)
    _printf_ll_oct                           0x08000d83   Thumb Code    12  _printf_oct_int_ll.o(.text)
    _sys_exit                                0x08000d99   Thumb Code     8  sys_exit.o(.text)
    __user_libspace                          0x08000da5   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000da5   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000da5   Thumb Code     0  libspace.o(.text)
    _ll_udiv10                               0x08000dad   Thumb Code   138  lludiv10.o(.text)
    __lib_sel_fp_printf                      0x08000e37   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08000fe9   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_fp_hex_real                      0x08001255   Thumb Code   756  _printf_fp_hex.o(.text)
    _printf_cs_common                        0x08001551   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08001565   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08001575   Thumb Code     8  _printf_char.o(.text)
    _printf_lcs_common                       0x0800157d   Thumb Code    20  _printf_wchar.o(.text)
    _printf_wchar                            0x08001591   Thumb Code    16  _printf_wchar.o(.text)
    _printf_wstring                          0x080015a1   Thumb Code     8  _printf_wchar.o(.text)
    _wcrtomb                                 0x080015a9   Thumb Code    64  _wcrtomb.o(.text)
    __I$use$semihosting                      0x080015e9   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x080015e9   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x080015eb   Thumb Code     0  indicate_semi.o(.text)
    __user_setup_stackheap                   0x080015eb   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_ctype_table                         0x08001635   Thumb Code    16  rt_ctype_table.o(.text)
    __rt_locale                              0x08001645   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _printf_fp_infnan                        0x0800164d   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x080016cd   Thumb Code   224  bigflt0.o(.text)
    exit                                     0x080017b1   Thumb Code    18  exit.o(.text)
    strcmp                                   0x080017c5   Thumb Code   128  strcmpv7m.o(.text)
    _btod_d2e                                0x08001845   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x08001883   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x080018c9   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08001929   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x08001c61   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08001d3d   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x08001d67   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x08001d91   Thumb Code   580  btod.o(CL$$btod_mult_common)
    BusFault_Handler                         0x08001fd5   Thumb Code     4  gd32f4xx_it.o(i.BusFault_Handler)
    DebugMon_Handler                         0x08001fd9   Thumb Code     4  gd32f4xx_it.o(i.DebugMon_Handler)
    HardFault_Handler                        0x08001fdd   Thumb Code     4  gd32f4xx_it.o(i.HardFault_Handler)
    MemManage_Handler                        0x08002109   Thumb Code     4  gd32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x0800210d   Thumb Code     4  gd32f4xx_it.o(i.NMI_Handler)
    OLED_Clear                               0x08002111   Thumb Code    56  oled.o(i.OLED_Clear)
    OLED_Init                                0x08002149   Thumb Code    42  oled.o(i.OLED_Init)
    OLED_Set_Position                        0x08002179   Thumb Code    36  oled.o(i.OLED_Set_Position)
    OLED_ShowChar                            0x0800219d   Thumb Code   148  oled.o(i.OLED_ShowChar)
    OLED_ShowStr                             0x08002239   Thumb Code    58  oled.o(i.OLED_ShowStr)
    OLED_Write_cmd                           0x08002275   Thumb Code   282  oled.o(i.OLED_Write_cmd)
    OLED_Write_data                          0x0800239d   Thumb Code   282  oled.o(i.OLED_Write_data)
    PendSV_Handler                           0x080024c5   Thumb Code     4  gd32f4xx_it.o(i.PendSV_Handler)
    SDIO_IRQHandler                          0x080024c9   Thumb Code     8  gd32f4xx_it.o(i.SDIO_IRQHandler)
    SVC_Handler                              0x080024d1   Thumb Code     4  gd32f4xx_it.o(i.SVC_Handler)
    $Super$$SysTick_Handler                  0x080024d5   Thumb Code     8  gd32f4xx_it.o(i.SysTick_Handler)
    SystemInit                               0x080024dd   Thumb Code   364  system_gd32f4xx.o(i.SystemInit)
    USART0_IRQHandler                        0x08002659   Thumb Code    94  gd32f4xx_it.o(i.USART0_IRQHandler)
    UsageFault_Handler                       0x080026d1   Thumb Code     4  gd32f4xx_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x080026d5   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __perf_counter_init                      0x0800272d   Thumb Code    10  perf_counter.o(i.__perf_counter_init)
    __perf_os_patch_init                     0x08002737   Thumb Code     2  perf_counter.o(i.__perf_os_patch_init)
    _is_digit                                0x08002739   Thumb Code    14  __printf_wp.o(i._is_digit)
    adc_calibration_enable                   0x08002763   Thumb Code    42  gd32f4xx_adc.o(i.adc_calibration_enable)
    adc_channel_length_config                0x0800278d   Thumb Code    82  gd32f4xx_adc.o(i.adc_channel_length_config)
    adc_clock_config                         0x080027e1   Thumb Code    28  gd32f4xx_adc.o(i.adc_clock_config)
    adc_data_alignment_config                0x08002805   Thumb Code    22  gd32f4xx_adc.o(i.adc_data_alignment_config)
    adc_dma_mode_enable                      0x0800281b   Thumb Code    10  gd32f4xx_adc.o(i.adc_dma_mode_enable)
    adc_dma_request_after_last_enable        0x08002825   Thumb Code    10  gd32f4xx_adc.o(i.adc_dma_request_after_last_enable)
    adc_enable                               0x0800282f   Thumb Code    18  gd32f4xx_adc.o(i.adc_enable)
    adc_external_trigger_config              0x08002841   Thumb Code    52  gd32f4xx_adc.o(i.adc_external_trigger_config)
    adc_external_trigger_source_config       0x08002875   Thumb Code    48  gd32f4xx_adc.o(i.adc_external_trigger_source_config)
    adc_routine_channel_config               0x080028a5   Thumb Code   172  gd32f4xx_adc.o(i.adc_routine_channel_config)
    adc_software_trigger_enable              0x08002951   Thumb Code    36  gd32f4xx_adc.o(i.adc_software_trigger_enable)
    adc_special_function_config              0x08002975   Thumb Code    90  gd32f4xx_adc.o(i.adc_special_function_config)
    adc_sync_mode_config                     0x080029d1   Thumb Code    28  gd32f4xx_adc.o(i.adc_sync_mode_config)
    adc_task                                 0x080029f5   Thumb Code    10  adc_app.o(i.adc_task)
    app_btn_init                             0x08002a09   Thumb Code    24  btn_app.o(i.app_btn_init)
    bsp_adc_init                             0x08002b35   Thumb Code   268  mcu_cmic_gd32f470vet6.o(i.bsp_adc_init)
    bsp_btn_init                             0x08002c51   Thumb Code    66  mcu_cmic_gd32f470vet6.o(i.bsp_btn_init)
    bsp_dac_init                             0x08002ca1   Thumb Code   234  mcu_cmic_gd32f470vet6.o(i.bsp_dac_init)
    bsp_gd25qxx_init                         0x08002d9d   Thumb Code   144  mcu_cmic_gd32f470vet6.o(i.bsp_gd25qxx_init)
    bsp_led_init                             0x08002e35   Thumb Code    50  mcu_cmic_gd32f470vet6.o(i.bsp_led_init)
    bsp_oled_init                            0x08002e6d   Thumb Code   226  mcu_cmic_gd32f470vet6.o(i.bsp_oled_init)
    bsp_rtc_init                             0x08002f65   Thumb Code    44  mcu_cmic_gd32f470vet6.o(i.bsp_rtc_init)
    bsp_rtc_pre_cfg                          0x08002f99   Thumb Code    52  mcu_cmic_gd32f470vet6.o(i.bsp_rtc_pre_cfg)
    bsp_rtc_setup                            0x08002fd5   Thumb Code    80  mcu_cmic_gd32f470vet6.o(i.bsp_rtc_setup)
    bsp_usart_init                           0x08003035   Thumb Code   272  mcu_cmic_gd32f470vet6.o(i.bsp_usart_init)
    btn_task                                 0x08003155   Thumb Code    14  btn_app.o(i.btn_task)
    card_info_get                            0x08003165   Thumb Code   366  sd_app.o(i.card_info_get)
    clust2sect                               0x08003995   Thumb Code    26  ff.o(i.clust2sect)
    dac_deinit                               0x08003c0d   Thumb Code    34  gd32f4xx_dac.o(i.dac_deinit)
    dac_dma_enable                           0x08003c35   Thumb Code    26  gd32f4xx_dac.o(i.dac_dma_enable)
    dac_enable                               0x08003c4f   Thumb Code    26  gd32f4xx_dac.o(i.dac_enable)
    dac_trigger_enable                       0x08003c69   Thumb Code    26  gd32f4xx_dac.o(i.dac_trigger_enable)
    dac_trigger_source_config                0x08003c83   Thumb Code    40  gd32f4xx_dac.o(i.dac_trigger_source_config)
    dac_wave_mode_config                     0x08003cab   Thumb Code    40  gd32f4xx_dac.o(i.dac_wave_mode_config)
    delay_1ms                                0x08003cd5   Thumb Code    16  systick.o(i.delay_1ms)
    delay_decrement                          0x08003ce9   Thumb Code    18  systick.o(i.delay_decrement)
    delay_ms                                 0x08003d01   Thumb Code    76  perf_counter.o(i.delay_ms)
    disk_initialize                          0x08003fd3   Thumb Code   134  diskio.o(i.disk_initialize)
    disk_ioctl                               0x08004059   Thumb Code     6  diskio.o(i.disk_ioctl)
    disk_read                                0x0800405f   Thumb Code    80  diskio.o(i.disk_read)
    disk_status                              0x080040af   Thumb Code    12  diskio.o(i.disk_status)
    disk_write                               0x080040bb   Thumb Code    80  diskio.o(i.disk_write)
    dma_channel_disable                      0x0800410b   Thumb Code    32  gd32f4xx_dma.o(i.dma_channel_disable)
    dma_channel_enable                       0x0800412b   Thumb Code    32  gd32f4xx_dma.o(i.dma_channel_enable)
    dma_channel_subperipheral_select         0x0800414b   Thumb Code    38  gd32f4xx_dma.o(i.dma_channel_subperipheral_select)
    dma_circulation_disable                  0x08004171   Thumb Code    32  gd32f4xx_dma.o(i.dma_circulation_disable)
    dma_circulation_enable                   0x08004191   Thumb Code    32  gd32f4xx_dma.o(i.dma_circulation_enable)
    dma_deinit                               0x080041b1   Thumb Code   166  gd32f4xx_dma.o(i.dma_deinit)
    dma_flag_clear                           0x08004257   Thumb Code    62  gd32f4xx_dma.o(i.dma_flag_clear)
    dma_flag_get                             0x08004295   Thumb Code    76  gd32f4xx_dma.o(i.dma_flag_get)
    dma_flow_controller_config               0x080042e1   Thumb Code    64  gd32f4xx_dma.o(i.dma_flow_controller_config)
    dma_memory_address_config                0x08004321   Thumb Code    32  gd32f4xx_dma.o(i.dma_memory_address_config)
    dma_multi_data_mode_init                 0x08004341   Thumb Code   352  gd32f4xx_dma.o(i.dma_multi_data_mode_init)
    dma_single_data_mode_init                0x08004559   Thumb Code   340  gd32f4xx_dma.o(i.dma_single_data_mode_init)
    dma_single_data_para_struct_init         0x080046b1   Thumb Code    34  gd32f4xx_dma.o(i.dma_single_data_para_struct_init)
    dma_transfer_number_config               0x08004789   Thumb Code    16  gd32f4xx_dma.o(i.dma_transfer_number_config)
    dma_transfer_number_get                  0x08004799   Thumb Code    16  gd32f4xx_dma.o(i.dma_transfer_number_get)
    ebtn_init                                0x08004801   Thumb Code    88  ebtn.o(i.ebtn_init)
    ebtn_process                             0x0800485d   Thumb Code    26  ebtn.o(i.ebtn_process)
    ebtn_process_with_curr_state             0x08004931   Thumb Code   444  ebtn.o(i.ebtn_process_with_curr_state)
    f_close                                  0x08004af7   Thumb Code    22  ff.o(i.f_close)
    f_mount                                  0x08004b0d   Thumb Code    38  ff.o(i.f_mount)
    f_open                                   0x08004b39   Thumb Code   364  ff.o(i.f_open)
    f_read                                   0x08004ca5   Thumb Code   462  ff.o(i.f_read)
    f_sync                                   0x08004e73   Thumb Code   184  ff.o(i.f_sync)
    f_write                                  0x08004f2b   Thumb Code   526  ff.o(i.f_write)
    gd32f4xx_firmware_version_get            0x080051d7   Thumb Code     6  system_gd32f4xx.o(i.gd32f4xx_firmware_version_get)
    get_fat                                  0x080051dd   Thumb Code   228  ff.o(i.get_fat)
    get_fattime                              0x080052c1   Thumb Code     4  diskio.o(i.get_fattime)
    get_system_ms                            0x080052c5   Thumb Code   112  perf_counter.o(i.get_system_ms)
    get_system_ticks                         0x08005345   Thumb Code    86  perf_counter.o(i.get_system_ticks)
    gpio_af_set                              0x080053a5   Thumb Code    94  gd32f4xx_gpio.o(i.gpio_af_set)
    gpio_bit_reset                           0x08005403   Thumb Code     4  gd32f4xx_gpio.o(i.gpio_bit_reset)
    gpio_bit_set                             0x08005407   Thumb Code     4  gd32f4xx_gpio.o(i.gpio_bit_set)
    gpio_input_bit_get                       0x08005481   Thumb Code    16  gd32f4xx_gpio.o(i.gpio_input_bit_get)
    gpio_mode_set                            0x08005491   Thumb Code    78  gd32f4xx_gpio.o(i.gpio_mode_set)
    gpio_output_options_set                  0x080054df   Thumb Code    66  gd32f4xx_gpio.o(i.gpio_output_options_set)
    i2c_ack_config                           0x08005521   Thumb Code    16  gd32f4xx_i2c.o(i.i2c_ack_config)
    i2c_clock_config                         0x08005531   Thumb Code   216  gd32f4xx_i2c.o(i.i2c_clock_config)
    i2c_deinit                               0x08005615   Thumb Code    84  gd32f4xx_i2c.o(i.i2c_deinit)
    i2c_dma_config                           0x0800566d   Thumb Code    16  gd32f4xx_i2c.o(i.i2c_dma_config)
    i2c_enable                               0x0800567d   Thumb Code    10  gd32f4xx_i2c.o(i.i2c_enable)
    i2c_flag_clear                           0x08005687   Thumb Code    40  gd32f4xx_i2c.o(i.i2c_flag_clear)
    i2c_flag_get                             0x080056af   Thumb Code    30  gd32f4xx_i2c.o(i.i2c_flag_get)
    i2c_master_addressing                    0x080056cd   Thumb Code    20  gd32f4xx_i2c.o(i.i2c_master_addressing)
    i2c_mode_addr_config                     0x080056e1   Thumb Code    28  gd32f4xx_i2c.o(i.i2c_mode_addr_config)
    i2c_start_on_bus                         0x080056fd   Thumb Code    10  gd32f4xx_i2c.o(i.i2c_start_on_bus)
    i2c_stop_on_bus                          0x08005707   Thumb Code    10  gd32f4xx_i2c.o(i.i2c_stop_on_bus)
    init_cycle_counter                       0x08005711   Thumb Code    98  perf_counter.o(i.init_cycle_counter)
    led_disp                                 0x0800578d   Thumb Code   220  led_app.o(i.led_disp)
    led_task                                 0x08005871   Thumb Code    10  led_app.o(i.led_task)
    main                                     0x08005881   Thumb Code    86  main.o(i.main)
    memory_compare                           0x0800592b   Thumb Code    36  sd_app.o(i.memory_compare)
    my_printf                                0x080059c1   Thumb Code    98  usart_app.o(i.my_printf)
    nvic_irq_enable                          0x08005a29   Thumb Code   186  gd32f4xx_misc.o(i.nvic_irq_enable)
    nvic_priority_group_set                  0x08005aed   Thumb Code    10  gd32f4xx_misc.o(i.nvic_priority_group_set)
    oled_printf                              0x08005b01   Thumb Code    60  oled_app.o(i.oled_printf)
    oled_task                                0x08005b3d   Thumb Code   134  oled_app.o(i.oled_task)
    perfc_port_clear_system_timer_ovf_pending 0x08005c01   Thumb Code    10  perfc_port_default.o(i.perfc_port_clear_system_timer_ovf_pending)
    perfc_port_get_system_timer_elapsed      0x08005c1d   Thumb Code    18  perfc_port_default.o(i.perfc_port_get_system_timer_elapsed)
    perfc_port_get_system_timer_freq         0x08005c31   Thumb Code     6  perfc_port_default.o(i.perfc_port_get_system_timer_freq)
    perfc_port_get_system_timer_top          0x08005c3d   Thumb Code    10  perfc_port_default.o(i.perfc_port_get_system_timer_top)
    perfc_port_init_system_timer             0x08005c47   Thumb Code    82  perfc_port_default.o(i.perfc_port_init_system_timer)
    perfc_port_insert_to_system_timer_insert_ovf_handler 0x08005c99   Thumb Code   222  perf_counter.o(i.perfc_port_insert_to_system_timer_insert_ovf_handler)
    perfc_port_is_system_timer_ovf_pending   0x08005d95   Thumb Code    10  perfc_port_default.o(i.perfc_port_is_system_timer_ovf_pending)
    pmu_backup_write_enable                  0x08005db1   Thumb Code    14  gd32f4xx_pmu.o(i.pmu_backup_write_enable)
    prv_btn_event                            0x08005dc5   Thumb Code   126  btn_app.o(i.prv_btn_event)
    prv_btn_get_state                        0x08005e49   Thumb Code   160  btn_app.o(i.prv_btn_get_state)
    put_fat                                  0x080062b5   Thumb Code   310  ff.o(i.put_fat)
    rcu_all_reset_flag_clear                 0x080066a5   Thumb Code    14  gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear)
    rcu_clock_freq_get                       0x080066b9   Thumb Code   264  gd32f4xx_rcu.o(i.rcu_clock_freq_get)
    rcu_flag_get                             0x08006801   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_flag_get)
    rcu_osci_on                              0x08006825   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_osci_on)
    rcu_osci_stab_wait                       0x08006849   Thumb Code   342  gd32f4xx_rcu.o(i.rcu_osci_stab_wait)
    rcu_periph_clock_enable                  0x080069a5   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_clock_enable)
    rcu_periph_reset_disable                 0x080069c9   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_reset_disable)
    rcu_periph_reset_enable                  0x080069ed   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_reset_enable)
    rcu_rtc_clock_config                     0x08006a11   Thumb Code    18  gd32f4xx_rcu.o(i.rcu_rtc_clock_config)
    rtc_current_time_get                     0x08006a91   Thumb Code    96  gd32f4xx_rtc.o(i.rtc_current_time_get)
    rtc_init                                 0x08006af5   Thumb Code   190  gd32f4xx_rtc.o(i.rtc_init)
    rtc_init_mode_enter                      0x08006bb9   Thumb Code    66  gd32f4xx_rtc.o(i.rtc_init_mode_enter)
    rtc_init_mode_exit                       0x08006c01   Thumb Code    14  gd32f4xx_rtc.o(i.rtc_init_mode_exit)
    rtc_register_sync_wait                   0x08006c15   Thumb Code    92  gd32f4xx_rtc.o(i.rtc_register_sync_wait)
    rtc_task                                 0x08006c75   Thumb Code    36  rtc_app.o(i.rtc_task)
    scheduler_init                           0x08006cb1   Thumb Code     8  scheduler.o(i.scheduler_init)
    scheduler_run                            0x08006cbd   Thumb Code    78  scheduler.o(i.scheduler_run)
    sd_block_read                            0x08006d15   Thumb Code   500  sdio_sdcard.o(i.sd_block_read)
    sd_block_write                           0x08006f2d   Thumb Code   760  sdio_sdcard.o(i.sd_block_write)
    sd_bus_mode_config                       0x0800724d   Thumb Code   144  sdio_sdcard.o(i.sd_bus_mode_config)
    sd_card_capacity_get                     0x080073dd   Thumb Code   160  sdio_sdcard.o(i.sd_card_capacity_get)
    sd_card_information_get                  0x08007485   Thumb Code   686  sdio_sdcard.o(i.sd_card_information_get)
    sd_card_init                             0x08007745   Thumb Code   268  sdio_sdcard.o(i.sd_card_init)
    sd_card_select_deselect                  0x08007861   Thumb Code    38  sdio_sdcard.o(i.sd_card_select_deselect)
    sd_cardstatus_get                        0x08007941   Thumb Code    66  sdio_sdcard.o(i.sd_cardstatus_get)
    sd_fatfs_init                            0x080079a1   Thumb Code    14  sd_app.o(i.sd_fatfs_init)
    sd_fatfs_test                            0x080079b1   Thumb Code   268  sd_app.o(i.sd_fatfs_test)
    sd_init                                  0x08007bdd   Thumb Code    70  sdio_sdcard.o(i.sd_init)
    sd_interrupts_process                    0x08007c25   Thumb Code   286  sdio_sdcard.o(i.sd_interrupts_process)
    sd_multiblocks_read                      0x08007d55   Thumb Code   632  sdio_sdcard.o(i.sd_multiblocks_read)
    sd_multiblocks_write                     0x08007ff1   Thumb Code   878  sdio_sdcard.o(i.sd_multiblocks_write)
    sd_power_on                              0x08008389   Thumb Code   290  sdio_sdcard.o(i.sd_power_on)
    sd_transfer_mode_config                  0x08008611   Thumb Code    20  sdio_sdcard.o(i.sd_transfer_mode_config)
    sd_transfer_stop                         0x08008629   Thumb Code    36  sdio_sdcard.o(i.sd_transfer_stop)
    sdio_bus_mode_set                        0x0800864d   Thumb Code    22  gd32f4xx_sdio.o(i.sdio_bus_mode_set)
    sdio_clock_config                        0x08008669   Thumb Code    44  gd32f4xx_sdio.o(i.sdio_clock_config)
    sdio_clock_enable                        0x0800869d   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_clock_enable)
    sdio_command_index_get                   0x080086b1   Thumb Code     8  gd32f4xx_sdio.o(i.sdio_command_index_get)
    sdio_command_response_config             0x080086bd   Thumb Code    52  gd32f4xx_sdio.o(i.sdio_command_response_config)
    sdio_csm_enable                          0x080086f5   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_csm_enable)
    sdio_data_config                         0x08008709   Thumb Code    54  gd32f4xx_sdio.o(i.sdio_data_config)
    sdio_data_read                           0x08008745   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_data_read)
    sdio_data_transfer_config                0x08008751   Thumb Code    24  gd32f4xx_sdio.o(i.sdio_data_transfer_config)
    sdio_data_write                          0x0800876d   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_data_write)
    sdio_deinit                              0x08008779   Thumb Code    20  gd32f4xx_sdio.o(i.sdio_deinit)
    sdio_dma_disable                         0x0800878d   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_dma_disable)
    sdio_dma_enable                          0x080087a1   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_dma_enable)
    sdio_dsm_disable                         0x080087b5   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_dsm_disable)
    sdio_dsm_enable                          0x080087c9   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_dsm_enable)
    sdio_flag_clear                          0x080087dd   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_flag_clear)
    sdio_flag_get                            0x080087e9   Thumb Code    16  gd32f4xx_sdio.o(i.sdio_flag_get)
    sdio_hardware_clock_disable              0x080087fd   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_hardware_clock_disable)
    sdio_interrupt_disable                   0x08008811   Thumb Code    12  gd32f4xx_sdio.o(i.sdio_interrupt_disable)
    sdio_interrupt_enable                    0x08008821   Thumb Code    12  gd32f4xx_sdio.o(i.sdio_interrupt_enable)
    sdio_interrupt_flag_clear                0x08008831   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_interrupt_flag_clear)
    sdio_interrupt_flag_get                  0x0800883d   Thumb Code    16  gd32f4xx_sdio.o(i.sdio_interrupt_flag_get)
    sdio_power_state_get                     0x08008851   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_power_state_get)
    sdio_power_state_set                     0x0800885d   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_power_state_set)
    sdio_response_get                        0x08008869   Thumb Code    56  gd32f4xx_sdio.o(i.sdio_response_get)
    sdio_wait_type_set                       0x080088a5   Thumb Code    22  gd32f4xx_sdio.o(i.sdio_wait_type_set)
    spi_dma_disable                          0x080088c1   Thumb Code    22  gd32f4xx_spi.o(i.spi_dma_disable)
    spi_dma_enable                           0x080088d7   Thumb Code    22  gd32f4xx_spi.o(i.spi_dma_enable)
    spi_enable                               0x080088ed   Thumb Code    10  gd32f4xx_spi.o(i.spi_enable)
    spi_flash_buffer_read                    0x080088f9   Thumb Code    80  gd25qxx.o(i.spi_flash_buffer_read)
    spi_flash_init                           0x0800894d   Thumb Code    20  gd25qxx.o(i.spi_flash_init)
    spi_flash_read_id                        0x08008969   Thumb Code    78  gd25qxx.o(i.spi_flash_read_id)
    spi_flash_send_byte_dma                  0x080089bd   Thumb Code   236  gd25qxx.o(i.spi_flash_send_byte_dma)
    spi_init                                 0x08008ab9   Thumb Code    50  gd32f4xx_spi.o(i.spi_init)
    systick_config                           0x08008ccd   Thumb Code    74  systick.o(i.systick_config)
    test_spi_flash                           0x08008d1d   Thumb Code   266  gd25qxx.o(i.test_spi_flash)
    timer5_config                            0x08009009   Thumb Code    68  mcu_cmic_gd32f470vet6.o(i.timer5_config)
    timer_deinit                             0x08009051   Thumb Code   374  gd32f4xx_timer.o(i.timer_deinit)
    timer_enable                             0x080091d5   Thumb Code    10  gd32f4xx_timer.o(i.timer_enable)
    timer_init                               0x080091e1   Thumb Code   122  gd32f4xx_timer.o(i.timer_init)
    timer_master_output_trigger_source_select 0x08009279   Thumb Code    16  gd32f4xx_timer.o(i.timer_master_output_trigger_source_select)
    timer_struct_para_init                   0x08009289   Thumb Code    22  gd32f4xx_timer.o(i.timer_struct_para_init)
    uart_task                                0x080092a1   Thumb Code    34  usart_app.o(i.uart_task)
    update_perf_counter                      0x080092d5   Thumb Code    94  perf_counter.o(i.update_perf_counter)
    usart_baudrate_set                       0x08009349   Thumb Code   224  gd32f4xx_usart.o(i.usart_baudrate_set)
    usart_data_receive                       0x08009431   Thumb Code    10  gd32f4xx_usart.o(i.usart_data_receive)
    usart_data_transmit                      0x0800943b   Thumb Code     8  gd32f4xx_usart.o(i.usart_data_transmit)
    usart_deinit                             0x08009445   Thumb Code   210  gd32f4xx_usart.o(i.usart_deinit)
    usart_dma_receive_config                 0x08009521   Thumb Code    20  gd32f4xx_usart.o(i.usart_dma_receive_config)
    usart_enable                             0x08009535   Thumb Code    10  gd32f4xx_usart.o(i.usart_enable)
    usart_flag_get                           0x0800953f   Thumb Code    30  gd32f4xx_usart.o(i.usart_flag_get)
    usart_interrupt_enable                   0x0800955d   Thumb Code    26  gd32f4xx_usart.o(i.usart_interrupt_enable)
    usart_interrupt_flag_get                 0x08009577   Thumb Code    56  gd32f4xx_usart.o(i.usart_interrupt_flag_get)
    usart_receive_config                     0x080095af   Thumb Code    16  gd32f4xx_usart.o(i.usart_receive_config)
    usart_transmit_config                    0x080095bf   Thumb Code    16  gd32f4xx_usart.o(i.usart_transmit_config)
    _get_lc_numeric                          0x080095f9   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _get_lc_ctype                            0x08009625   Thumb Code    44  lc_ctype_c.o(locale$$code)
    __fpl_dretinf                            0x08009651   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_f2d                              0x0800965d   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x0800965d   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x080096b3   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x0800973f   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x08009747   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x08009747   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    _printf_fp_dec                           0x08009749   Thumb Code     4  printf1.o(x$fpl$printf1)
    _printf_fp_hex                           0x0800974d   Thumb Code     4  printf2.o(x$fpl$printf2)
    F6X8                                     0x08009750   Data         552  oled.o(.constdata)
    __I$use$fp                               0x08009750   Number         0  usenofp.o(x$fpl$usenofp)
    Hzk                                      0x08009f68   Data         128  oled.o(.constdata)
    Hzb                                      0x08009fe8   Data         512  oled.o(.constdata)
    Region$$Table$$Base                      0x0800a2f4   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800a314   Number         0  anon$$obj.o(Region$$Table)
    __ctype                                  0x0800a33d   Data           0  lc_ctype_c.o(locale$$data)
    SHT$$INIT_ARRAY$$Base                    0x0800a440   Number         0  perf_counter.o(.init_array)
    SHT$$INIT_ARRAY$$Limit                   0x0800a444   Number         0  init_aeabi.o(.init_array)
    oled_cmd_buf                             0x20000004   Data           2  mcu_cmic_gd32f470vet6.o(.data)
    oled_data_buf                            0x20000006   Data           2  mcu_cmic_gd32f470vet6.o(.data)
    adc_value                                0x20000008   Data           2  mcu_cmic_gd32f470vet6.o(.data)
    convertarr                               0x2000000a   Data           2  mcu_cmic_gd32f470vet6.o(.data)
    prescaler_a                              0x2000000c   Data           4  mcu_cmic_gd32f470vet6.o(.data)
    prescaler_s                              0x20000010   Data           4  mcu_cmic_gd32f470vet6.o(.data)
    RTCSRC_FLAG                              0x20000014   Data           4  mcu_cmic_gd32f470vet6.o(.data)
    initcmd1                                 0x20000018   Data          22  oled.o(.data)
    sd_scr                                   0x20000030   Data           8  sdio_sdcard.o(.data)
    ucLed                                    0x20000120   Data           6  led_app.o(.data)
    task_num                                 0x20000128   Data           1  scheduler.o(.data)
    tx_count                                 0x20000174   Data           2  usart_app.o(.data)
    rx_flag                                  0x20000176   Data           1  usart_app.o(.data)
    i                                        0x20000178   Data           2  sd_app.o(.data)
    count                                    0x2000017a   Data           2  sd_app.o(.data)
    result                                   0x2000017c   Data           2  sd_app.o(.data)
    br                                       0x20000180   Data           4  sd_app.o(.data)
    bw                                       0x20000184   Data           4  sd_app.o(.data)
    SystemCoreClock                          0x20000188   Data           4  system_gd32f4xx.o(.data)
    g_nOffset                                0x200001d0   Data           4  perf_counter.o(.data)
    g_lLastTimeStamp                         0x200001d8   Data           8  perf_counter.o(.data)
    spi1_send_array                          0x200001e0   Data          12  mcu_cmic_gd32f470vet6.o(.bss)
    spi1_receive_array                       0x200001ec   Data          12  mcu_cmic_gd32f470vet6.o(.bss)
    rxbuffer                                 0x200001f8   Data         512  mcu_cmic_gd32f470vet6.o(.bss)
    rtc_initpara                             0x200003f8   Data          20  mcu_cmic_gd32f470vet6.o(.bss)
    rtc_alarm                                0x2000040c   Data          16  mcu_cmic_gd32f470vet6.o(.bss)
    uart_dma_buffer                          0x20000478   Data         512  usart_app.o(.bss)
    fs                                       0x20000678   Data         560  sd_app.o(.bss)
    fdst                                     0x200008a8   Data         548  sd_app.o(.bss)
    sd_cardinfo                              0x20000acc   Data          72  sd_app.o(.bss)
    buffer                                   0x20000b14   Data         128  sd_app.o(.bss)
    filebuffer                               0x20000b94   Data         128  sd_app.o(.bss)
    __libspace_start                         0x20000c14   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000c74   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001ad

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000a624, Max: 0x00080000, ABSOLUTE, COMPRESSED[0x0000a4b8])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000a444, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000001ac   Data   RO         8005    RESET               startup_gd32f450_470.o
    0x080001ac   0x080001ac   0x00000008   Code   RO         8546  * !!!main             c_w.l(__main.o)
    0x080001b4   0x080001b4   0x00000034   Code   RO         8947    !!!scatter          c_w.l(__scatter.o)
    0x080001e8   0x080001e8   0x0000005a   Code   RO         8945    !!dczerorl2         c_w.l(__dczerorl2.o)
    0x08000242   0x08000242   0x00000002   PAD
    0x08000244   0x08000244   0x0000001c   Code   RO         8949    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000260   0x08000260   0x00000000   Code   RO         8519    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x08000260   0x08000260   0x00000006   Code   RO         8617    .ARM.Collect$$_printf_percent$$00000001  c_w.l(_printf_n.o)
    0x08000266   0x08000266   0x00000006   Code   RO         8618    .ARM.Collect$$_printf_percent$$00000002  c_w.l(_printf_p.o)
    0x0800026c   0x0800026c   0x00000006   Code   RO         8621    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x08000272   0x08000272   0x00000006   Code   RO         8622    .ARM.Collect$$_printf_percent$$00000004  c_w.l(_printf_e.o)
    0x08000278   0x08000278   0x00000006   Code   RO         8623    .ARM.Collect$$_printf_percent$$00000005  c_w.l(_printf_g.o)
    0x0800027e   0x0800027e   0x00000006   Code   RO         8624    .ARM.Collect$$_printf_percent$$00000006  c_w.l(_printf_a.o)
    0x08000284   0x08000284   0x0000000a   Code   RO         8629    .ARM.Collect$$_printf_percent$$00000007  c_w.l(_printf_ll.o)
    0x0800028e   0x0800028e   0x00000006   Code   RO         8620    .ARM.Collect$$_printf_percent$$00000008  c_w.l(_printf_i.o)
    0x08000294   0x08000294   0x00000006   Code   RO         8517    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x0800029a   0x0800029a   0x00000006   Code   RO         8518    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x080002a0   0x080002a0   0x00000006   Code   RO         8619    .ARM.Collect$$_printf_percent$$0000000B  c_w.l(_printf_o.o)
    0x080002a6   0x080002a6   0x00000006   Code   RO         8516    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x080002ac   0x080002ac   0x00000006   Code   RO         8626    .ARM.Collect$$_printf_percent$$0000000D  c_w.l(_printf_lli.o)
    0x080002b2   0x080002b2   0x00000006   Code   RO         8627    .ARM.Collect$$_printf_percent$$0000000E  c_w.l(_printf_lld.o)
    0x080002b8   0x080002b8   0x00000006   Code   RO         8628    .ARM.Collect$$_printf_percent$$0000000F  c_w.l(_printf_llu.o)
    0x080002be   0x080002be   0x00000006   Code   RO         8633    .ARM.Collect$$_printf_percent$$00000010  c_w.l(_printf_llo.o)
    0x080002c4   0x080002c4   0x00000006   Code   RO         8634    .ARM.Collect$$_printf_percent$$00000011  c_w.l(_printf_llx.o)
    0x080002ca   0x080002ca   0x0000000a   Code   RO         8630    .ARM.Collect$$_printf_percent$$00000012  c_w.l(_printf_l.o)
    0x080002d4   0x080002d4   0x00000006   Code   RO         8615    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x080002da   0x080002da   0x00000006   Code   RO         8616    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x080002e0   0x080002e0   0x00000006   Code   RO         8631    .ARM.Collect$$_printf_percent$$00000015  c_w.l(_printf_lc.o)
    0x080002e6   0x080002e6   0x00000006   Code   RO         8632    .ARM.Collect$$_printf_percent$$00000016  c_w.l(_printf_ls.o)
    0x080002ec   0x080002ec   0x00000004   Code   RO         8625    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x080002f0   0x080002f0   0x00000002   Code   RO         8864    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080002f2   0x080002f2   0x00000004   Code   RO         8647    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x080002f6   0x080002f6   0x00000000   Code   RO         8650    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080002f6   0x080002f6   0x00000000   Code   RO         8653    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080002f6   0x080002f6   0x00000000   Code   RO         8655    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080002f6   0x080002f6   0x00000000   Code   RO         8657    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080002f6   0x080002f6   0x00000006   Code   RO         8658    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x080002fc   0x080002fc   0x00000000   Code   RO         8660    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080002fc   0x080002fc   0x0000000c   Code   RO         8661    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x08000308   0x08000308   0x00000000   Code   RO         8662    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000308   0x08000308   0x00000000   Code   RO         8664    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000308   0x08000308   0x0000000a   Code   RO         8665    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         8666    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         8668    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         8670    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         8672    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         8674    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         8676    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         8678    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         8680    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         8684    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         8686    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         8688    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000004   Code   RO         8689    .ARM.Collect$$libinit$$00000031  c_w.l(libinit2.o)
    0x08000316   0x08000316   0x00000000   Code   RO         8690    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000316   0x08000316   0x00000002   Code   RO         8691    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000318   0x08000318   0x00000002   Code   RO         8942    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x0800031a   0x0800031a   0x00000000   Code   RO         8866    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x0800031a   0x0800031a   0x00000000   Code   RO         8868    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x0800031a   0x0800031a   0x00000000   Code   RO         8870    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x0800031a   0x0800031a   0x00000000   Code   RO         8873    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x0800031a   0x0800031a   0x00000000   Code   RO         8876    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x0800031a   0x0800031a   0x00000000   Code   RO         8878    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x0800031a   0x0800031a   0x00000000   Code   RO         8881    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x0800031a   0x0800031a   0x00000002   Code   RO         8882    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x0800031c   0x0800031c   0x00000000   Code   RO         8558    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x0800031c   0x0800031c   0x00000000   Code   RO         8715    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x0800031c   0x0800031c   0x00000006   Code   RO         8727    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x08000322   0x08000322   0x00000000   Code   RO         8717    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x08000322   0x08000322   0x00000004   Code   RO         8718    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000326   0x08000326   0x00000000   Code   RO         8720    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000326   0x08000326   0x00000008   Code   RO         8721    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800032e   0x0800032e   0x00000002   Code   RO         8887    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000330   0x08000330   0x00000000   Code   RO         8913    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000330   0x08000330   0x00000004   Code   RO         8914    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000334   0x08000334   0x00000006   Code   RO         8915    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x0800033a   0x0800033a   0x00000002   PAD
    0x0800033c   0x0800033c   0x00000040   Code   RO         8006    .text               startup_gd32f450_470.o
    0x0800037c   0x0800037c   0x00000020   Code   RO         8391    .text               systick_wrapper_ual.o
    0x0800039c   0x0800039c   0x00000048   Code   RO         8459    .text               c_w.l(llsdiv.o)
    0x080003e4   0x080003e4   0x00000034   Code   RO         8461    .text               c_w.l(vsnprintf.o)
    0x08000418   0x08000418   0x0000002c   Code   RO         8465    .text               c_w.l(__2sprintf.o)
    0x08000444   0x08000444   0x00000078   Code   RO         8473    .text               c_w.l(_printf_dec.o)
    0x080004bc   0x080004bc   0x00000094   Code   RO         8493    .text               c_w.l(_printf_hex_int_ll_ptr.o)
    0x08000550   0x08000550   0x00000188   Code   RO         8513    .text               c_w.l(__printf_flags_ss_wp.o)
    0x080006d8   0x080006d8   0x00000058   Code   RO         8528    .text               c_w.l(memcmp.o)
    0x08000730   0x08000730   0x0000003e   Code   RO         8532    .text               c_w.l(strlen.o)
    0x0800076e   0x0800076e   0x0000008a   Code   RO         8534    .text               c_w.l(rt_memcpy_v6.o)
    0x080007f8   0x080007f8   0x00000064   Code   RO         8536    .text               c_w.l(rt_memcpy_w.o)
    0x0800085c   0x0800085c   0x00000044   Code   RO         8540    .text               c_w.l(rt_memclr.o)
    0x080008a0   0x080008a0   0x0000004e   Code   RO         8542    .text               c_w.l(rt_memclr_w.o)
    0x080008ee   0x080008ee   0x00000006   Code   RO         8544    .text               c_w.l(heapauxi.o)
    0x080008f4   0x080008f4   0x00000024   Code   RO         8551    .text               c_w.l(init_aeabi.o)
    0x08000918   0x08000918   0x000000ee   Code   RO         8584    .text               c_w.l(lludivv7m.o)
    0x08000a06   0x08000a06   0x0000004e   Code   RO         8586    .text               c_w.l(_printf_pad.o)
    0x08000a54   0x08000a54   0x00000024   Code   RO         8588    .text               c_w.l(_printf_truncate.o)
    0x08000a78   0x08000a78   0x00000052   Code   RO         8590    .text               c_w.l(_printf_str.o)
    0x08000aca   0x08000aca   0x000000b2   Code   RO         8592    .text               c_w.l(_printf_intcommon.o)
    0x08000b7c   0x08000b7c   0x00000028   Code   RO         8594    .text               c_w.l(_printf_charcount.o)
    0x08000ba4   0x08000ba4   0x00000030   Code   RO         8596    .text               c_w.l(_printf_char_common.o)
    0x08000bd4   0x08000bd4   0x0000000a   Code   RO         8598    .text               c_w.l(_sputc.o)
    0x08000bde   0x08000bde   0x00000010   Code   RO         8600    .text               c_w.l(_snputc.o)
    0x08000bee   0x08000bee   0x00000002   PAD
    0x08000bf0   0x08000bf0   0x000000bc   Code   RO         8604    .text               c_w.l(_printf_wctomb.o)
    0x08000cac   0x08000cac   0x0000007c   Code   RO         8607    .text               c_w.l(_printf_longlong_dec.o)
    0x08000d28   0x08000d28   0x00000070   Code   RO         8613    .text               c_w.l(_printf_oct_int_ll.o)
    0x08000d98   0x08000d98   0x0000000c   Code   RO         8709    .text               c_w.l(sys_exit.o)
    0x08000da4   0x08000da4   0x00000008   Code   RO         8711    .text               c_w.l(libspace.o)
    0x08000dac   0x08000dac   0x0000008a   Code   RO         8741    .text               c_w.l(lludiv10.o)
    0x08000e36   0x08000e36   0x0000041e   Code   RO         8743    .text               c_w.l(_printf_fp_dec.o)
    0x08001254   0x08001254   0x000002fc   Code   RO         8745    .text               c_w.l(_printf_fp_hex.o)
    0x08001550   0x08001550   0x0000002c   Code   RO         8750    .text               c_w.l(_printf_char.o)
    0x0800157c   0x0800157c   0x0000002c   Code   RO         8752    .text               c_w.l(_printf_wchar.o)
    0x080015a8   0x080015a8   0x00000040   Code   RO         8762    .text               c_w.l(_wcrtomb.o)
    0x080015e8   0x080015e8   0x00000002   Code   RO         8784    .text               c_w.l(use_no_semi.o)
    0x080015ea   0x080015ea   0x00000000   Code   RO         8786    .text               c_w.l(indicate_semi.o)
    0x080015ea   0x080015ea   0x0000004a   Code   RO         8787    .text               c_w.l(sys_stackheap_outer.o)
    0x08001634   0x08001634   0x00000010   Code   RO         8789    .text               c_w.l(rt_ctype_table.o)
    0x08001644   0x08001644   0x00000008   Code   RO         8798    .text               c_w.l(rt_locale_intlibspace.o)
    0x0800164c   0x0800164c   0x00000080   Code   RO         8802    .text               c_w.l(_printf_fp_infnan.o)
    0x080016cc   0x080016cc   0x000000e4   Code   RO         8804    .text               c_w.l(bigflt0.o)
    0x080017b0   0x080017b0   0x00000012   Code   RO         8843    .text               c_w.l(exit.o)
    0x080017c2   0x080017c2   0x00000002   PAD
    0x080017c4   0x080017c4   0x00000080   Code   RO         8910    .text               c_w.l(strcmpv7m.o)
    0x08001844   0x08001844   0x0000003e   Code   RO         8807    CL$$btod_d2e        c_w.l(btod.o)
    0x08001882   0x08001882   0x00000046   Code   RO         8809    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x080018c8   0x080018c8   0x00000060   Code   RO         8808    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08001928   0x08001928   0x00000338   Code   RO         8817    CL$$btod_div_common  c_w.l(btod.o)
    0x08001c60   0x08001c60   0x000000dc   Code   RO         8814    CL$$btod_e2e        c_w.l(btod.o)
    0x08001d3c   0x08001d3c   0x0000002a   Code   RO         8811    CL$$btod_ediv       c_w.l(btod.o)
    0x08001d66   0x08001d66   0x0000002a   Code   RO         8810    CL$$btod_emul       c_w.l(btod.o)
    0x08001d90   0x08001d90   0x00000244   Code   RO         8816    CL$$btod_mult_common  c_w.l(btod.o)
    0x08001fd4   0x08001fd4   0x00000004   Code   RO            4    i.BusFault_Handler  gd32f4xx_it.o
    0x08001fd8   0x08001fd8   0x00000004   Code   RO            5    i.DebugMon_Handler  gd32f4xx_it.o
    0x08001fdc   0x08001fdc   0x00000004   Code   RO            6    i.HardFault_Handler  gd32f4xx_it.o
    0x08001fe0   0x08001fe0   0x00000128   Code   RO         1308    i.I2C_Bus_Reset     oled.o
    0x08002108   0x08002108   0x00000004   Code   RO            7    i.MemManage_Handler  gd32f4xx_it.o
    0x0800210c   0x0800210c   0x00000004   Code   RO            8    i.NMI_Handler       gd32f4xx_it.o
    0x08002110   0x08002110   0x00000038   Code   RO         1310    i.OLED_Clear        oled.o
    0x08002148   0x08002148   0x00000030   Code   RO         1313    i.OLED_Init         oled.o
    0x08002178   0x08002178   0x00000024   Code   RO         1315    i.OLED_Set_Position  oled.o
    0x0800219c   0x0800219c   0x0000009c   Code   RO         1316    i.OLED_ShowChar     oled.o
    0x08002238   0x08002238   0x0000003a   Code   RO         1322    i.OLED_ShowStr      oled.o
    0x08002272   0x08002272   0x00000002   PAD
    0x08002274   0x08002274   0x00000128   Code   RO         1323    i.OLED_Write_cmd    oled.o
    0x0800239c   0x0800239c   0x00000128   Code   RO         1324    i.OLED_Write_data   oled.o
    0x080024c4   0x080024c4   0x00000004   Code   RO            9    i.PendSV_Handler    gd32f4xx_it.o
    0x080024c8   0x080024c8   0x00000008   Code   RO           10    i.SDIO_IRQHandler   gd32f4xx_it.o
    0x080024d0   0x080024d0   0x00000004   Code   RO           11    i.SVC_Handler       gd32f4xx_it.o
    0x080024d4   0x080024d4   0x00000008   Code   RO           12    i.SysTick_Handler   gd32f4xx_it.o
    0x080024dc   0x080024dc   0x0000017c   Code   RO         8014    i.SystemInit        system_gd32f4xx.o
    0x08002658   0x08002658   0x00000078   Code   RO           13    i.USART0_IRQHandler  gd32f4xx_it.o
    0x080026d0   0x080026d0   0x00000004   Code   RO           14    i.UsageFault_Handler  gd32f4xx_it.o
    0x080026d4   0x080026d4   0x00000030   Code   RO         8885    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x08002704   0x08002704   0x00000028   Code   RO          331    i.__NVIC_SetPriority  systick.o
    0x0800272c   0x0800272c   0x0000000a   Code   RO         8075    i.__perf_counter_init  perf_counter.o
    0x08002736   0x08002736   0x00000002   Code   RO         8076    i.__perf_os_patch_init  perf_counter.o
    0x08002738   0x08002738   0x0000000e   Code   RO         8506    i._is_digit         c_w.l(__printf_wp.o)
    0x08002746   0x08002746   0x0000001c   Code   RO         8015    i._soft_delay_      system_gd32f4xx.o
    0x08002762   0x08002762   0x0000002a   Code   RO         2410    i.adc_calibration_enable  gd32f4xx_adc.o
    0x0800278c   0x0800278c   0x00000052   Code   RO         2412    i.adc_channel_length_config  gd32f4xx_adc.o
    0x080027de   0x080027de   0x00000002   PAD
    0x080027e0   0x080027e0   0x00000024   Code   RO         2413    i.adc_clock_config  gd32f4xx_adc.o
    0x08002804   0x08002804   0x00000016   Code   RO         2414    i.adc_data_alignment_config  gd32f4xx_adc.o
    0x0800281a   0x0800281a   0x0000000a   Code   RO         2419    i.adc_dma_mode_enable  gd32f4xx_adc.o
    0x08002824   0x08002824   0x0000000a   Code   RO         2421    i.adc_dma_request_after_last_enable  gd32f4xx_adc.o
    0x0800282e   0x0800282e   0x00000012   Code   RO         2422    i.adc_enable        gd32f4xx_adc.o
    0x08002840   0x08002840   0x00000034   Code   RO         2424    i.adc_external_trigger_config  gd32f4xx_adc.o
    0x08002874   0x08002874   0x00000030   Code   RO         2425    i.adc_external_trigger_source_config  gd32f4xx_adc.o
    0x080028a4   0x080028a4   0x000000ac   Code   RO         2440    i.adc_routine_channel_config  gd32f4xx_adc.o
    0x08002950   0x08002950   0x00000024   Code   RO         2443    i.adc_software_trigger_enable  gd32f4xx_adc.o
    0x08002974   0x08002974   0x0000005a   Code   RO         2444    i.adc_special_function_config  gd32f4xx_adc.o
    0x080029ce   0x080029ce   0x00000002   PAD
    0x080029d0   0x080029d0   0x00000024   Code   RO         2449    i.adc_sync_mode_config  gd32f4xx_adc.o
    0x080029f4   0x080029f4   0x00000014   Code   RO         2385    i.adc_task          adc_app.o
    0x08002a08   0x08002a08   0x00000024   Code   RO         2130    i.app_btn_init      btn_app.o
    0x08002a2c   0x08002a2c   0x00000026   Code   RO         1431    i.bit_array_and     ebtn.o
    0x08002a52   0x08002a52   0x0000002e   Code   RO         1432    i.bit_array_assign  ebtn.o
    0x08002a80   0x08002a80   0x00000024   Code   RO         1433    i.bit_array_cmp     ebtn.o
    0x08002aa4   0x08002aa4   0x00000016   Code   RO         1434    i.bit_array_get     ebtn.o
    0x08002aba   0x08002aba   0x00000054   Code   RO         1435    i.bit_array_num_bits_set  ebtn.o
    0x08002b0e   0x08002b0e   0x00000026   Code   RO         1436    i.bit_array_or      ebtn.o
    0x08002b34   0x08002b34   0x0000011c   Code   RO          378    i.bsp_adc_init      mcu_cmic_gd32f470vet6.o
    0x08002c50   0x08002c50   0x00000050   Code   RO          379    i.bsp_btn_init      mcu_cmic_gd32f470vet6.o
    0x08002ca0   0x08002ca0   0x000000fc   Code   RO          380    i.bsp_dac_init      mcu_cmic_gd32f470vet6.o
    0x08002d9c   0x08002d9c   0x00000098   Code   RO          381    i.bsp_gd25qxx_init  mcu_cmic_gd32f470vet6.o
    0x08002e34   0x08002e34   0x00000038   Code   RO          382    i.bsp_led_init      mcu_cmic_gd32f470vet6.o
    0x08002e6c   0x08002e6c   0x000000f8   Code   RO          383    i.bsp_oled_init     mcu_cmic_gd32f470vet6.o
    0x08002f64   0x08002f64   0x00000034   Code   RO          384    i.bsp_rtc_init      mcu_cmic_gd32f470vet6.o
    0x08002f98   0x08002f98   0x0000003c   Code   RO          385    i.bsp_rtc_pre_cfg   mcu_cmic_gd32f470vet6.o
    0x08002fd4   0x08002fd4   0x00000060   Code   RO          386    i.bsp_rtc_setup     mcu_cmic_gd32f470vet6.o
    0x08003034   0x08003034   0x00000120   Code   RO          387    i.bsp_usart_init    mcu_cmic_gd32f470vet6.o
    0x08003154   0x08003154   0x0000000e   Code   RO         2131    i.btn_task          btn_app.o
    0x08003162   0x08003162   0x00000002   PAD
    0x08003164   0x08003164   0x000003c8   Code   RO         2314    i.card_info_get     sd_app.o
    0x0800352c   0x0800352c   0x00000090   Code   RO         1845    i.check_fs          ff.o
    0x080035bc   0x080035bc   0x00000030   Code   RO         8081    i.check_systick     perf_counter.o
    0x080035ec   0x080035ec   0x00000014   Code   RO         1846    i.chk_chr           ff.o
    0x08003600   0x08003600   0x00000394   Code   RO         1847    i.chk_mounted       ff.o
    0x08003994   0x08003994   0x0000001a   Code   RO         1848    i.clust2sect        ff.o
    0x080039ae   0x080039ae   0x00000002   PAD
    0x080039b0   0x080039b0   0x00000030   Code   RO         1628    i.cmdsent_error_check  sdio_sdcard.o
    0x080039e0   0x080039e0   0x000000ca   Code   RO         1849    i.create_chain      ff.o
    0x08003aaa   0x08003aaa   0x00000002   PAD
    0x08003aac   0x08003aac   0x00000160   Code   RO         1850    i.create_name       ff.o
    0x08003c0c   0x08003c0c   0x00000028   Code   RO         3089    i.dac_deinit        gd32f4xx_dac.o
    0x08003c34   0x08003c34   0x0000001a   Code   RO         3092    i.dac_dma_enable    gd32f4xx_dac.o
    0x08003c4e   0x08003c4e   0x0000001a   Code   RO         3093    i.dac_enable        gd32f4xx_dac.o
    0x08003c68   0x08003c68   0x0000001a   Code   RO         3107    i.dac_trigger_enable  gd32f4xx_dac.o
    0x08003c82   0x08003c82   0x00000028   Code   RO         3108    i.dac_trigger_source_config  gd32f4xx_dac.o
    0x08003caa   0x08003caa   0x00000028   Code   RO         3109    i.dac_wave_mode_config  gd32f4xx_dac.o
    0x08003cd2   0x08003cd2   0x00000002   PAD
    0x08003cd4   0x08003cd4   0x00000014   Code   RO          332    i.delay_1ms         systick.o
    0x08003ce8   0x08003ce8   0x00000018   Code   RO          333    i.delay_decrement   systick.o
    0x08003d00   0x08003d00   0x00000054   Code   RO         8083    i.delay_ms          perf_counter.o
    0x08003d54   0x08003d54   0x0000005c   Code   RO         1851    i.dir_find          ff.o
    0x08003db0   0x08003db0   0x00000118   Code   RO         1852    i.dir_next          ff.o
    0x08003ec8   0x08003ec8   0x0000006e   Code   RO         1854    i.dir_register      ff.o
    0x08003f36   0x08003f36   0x0000009c   Code   RO         1856    i.dir_sdi           ff.o
    0x08003fd2   0x08003fd2   0x00000086   Code   RO         2076    i.disk_initialize   diskio.o
    0x08004058   0x08004058   0x00000006   Code   RO         2077    i.disk_ioctl        diskio.o
    0x0800405e   0x0800405e   0x00000050   Code   RO         2078    i.disk_read         diskio.o
    0x080040ae   0x080040ae   0x0000000c   Code   RO         2079    i.disk_status       diskio.o
    0x080040ba   0x080040ba   0x00000050   Code   RO         2080    i.disk_write        diskio.o
    0x0800410a   0x0800410a   0x00000020   Code   RO         3478    i.dma_channel_disable  gd32f4xx_dma.o
    0x0800412a   0x0800412a   0x00000020   Code   RO         3479    i.dma_channel_enable  gd32f4xx_dma.o
    0x0800414a   0x0800414a   0x00000026   Code   RO         3480    i.dma_channel_subperipheral_select  gd32f4xx_dma.o
    0x08004170   0x08004170   0x00000020   Code   RO         3481    i.dma_circulation_disable  gd32f4xx_dma.o
    0x08004190   0x08004190   0x00000020   Code   RO         3482    i.dma_circulation_enable  gd32f4xx_dma.o
    0x080041b0   0x080041b0   0x000000a6   Code   RO         3483    i.dma_deinit        gd32f4xx_dma.o
    0x08004256   0x08004256   0x0000003e   Code   RO         3485    i.dma_flag_clear    gd32f4xx_dma.o
    0x08004294   0x08004294   0x0000004c   Code   RO         3486    i.dma_flag_get      gd32f4xx_dma.o
    0x080042e0   0x080042e0   0x00000040   Code   RO         3487    i.dma_flow_controller_config  gd32f4xx_dma.o
    0x08004320   0x08004320   0x00000020   Code   RO         3492    i.dma_memory_address_config  gd32f4xx_dma.o
    0x08004340   0x08004340   0x00000164   Code   RO         3496    i.dma_multi_data_mode_init  gd32f4xx_dma.o
    0x080044a4   0x080044a4   0x000000b4   Code   RO         1629    i.dma_receive_config  sdio_sdcard.o
    0x08004558   0x08004558   0x00000158   Code   RO         3503    i.dma_single_data_mode_init  gd32f4xx_dma.o
    0x080046b0   0x080046b0   0x00000022   Code   RO         3504    i.dma_single_data_para_struct_init  gd32f4xx_dma.o
    0x080046d2   0x080046d2   0x00000002   PAD
    0x080046d4   0x080046d4   0x000000b4   Code   RO         1630    i.dma_transfer_config  sdio_sdcard.o
    0x08004788   0x08004788   0x00000010   Code   RO         3508    i.dma_transfer_number_config  gd32f4xx_dma.o
    0x08004798   0x08004798   0x00000010   Code   RO         3509    i.dma_transfer_number_get  gd32f4xx_dma.o
    0x080047a8   0x080047a8   0x00000058   Code   RO         1447    i.ebtn_get_current_state  ebtn.o
    0x08004800   0x08004800   0x0000005c   Code   RO         1449    i.ebtn_init         ebtn.o
    0x0800485c   0x0800485c   0x0000001a   Code   RO         1453    i.ebtn_process      ebtn.o
    0x08004876   0x08004876   0x0000003e   Code   RO         1454    i.ebtn_process_btn  ebtn.o
    0x080048b4   0x080048b4   0x0000007c   Code   RO         1455    i.ebtn_process_btn_combo  ebtn.o
    0x08004930   0x08004930   0x000001c0   Code   RO         1456    i.ebtn_process_with_curr_state  ebtn.o
    0x08004af0   0x08004af0   0x00000006   Code   RO         1460    i.ebtn_timer_sub    ebtn.o
    0x08004af6   0x08004af6   0x00000016   Code   RO         1858    i.f_close           ff.o
    0x08004b0c   0x08004b0c   0x0000002c   Code   RO         1862    i.f_mount           ff.o
    0x08004b38   0x08004b38   0x0000016c   Code   RO         1863    i.f_open            ff.o
    0x08004ca4   0x08004ca4   0x000001ce   Code   RO         1865    i.f_read            ff.o
    0x08004e72   0x08004e72   0x000000b8   Code   RO         1869    i.f_sync            ff.o
    0x08004f2a   0x08004f2a   0x0000020e   Code   RO         1873    i.f_write           ff.o
    0x08005138   0x08005138   0x0000009e   Code   RO         1874    i.follow_path       ff.o
    0x080051d6   0x080051d6   0x00000006   Code   RO         8016    i.gd32f4xx_firmware_version_get  system_gd32f4xx.o
    0x080051dc   0x080051dc   0x000000e4   Code   RO         1875    i.get_fat           ff.o
    0x080052c0   0x080052c0   0x00000004   Code   RO         2081    i.get_fattime       diskio.o
    0x080052c4   0x080052c4   0x00000080   Code   RO         8088    i.get_system_ms     perf_counter.o
    0x08005344   0x08005344   0x00000060   Code   RO         8089    i.get_system_ticks  perf_counter.o
    0x080053a4   0x080053a4   0x0000005e   Code   RO         4921    i.gpio_af_set       gd32f4xx_gpio.o
    0x08005402   0x08005402   0x00000004   Code   RO         4922    i.gpio_bit_reset    gd32f4xx_gpio.o
    0x08005406   0x08005406   0x00000004   Code   RO         4923    i.gpio_bit_set      gd32f4xx_gpio.o
    0x0800540a   0x0800540a   0x00000002   PAD
    0x0800540c   0x0800540c   0x00000074   Code   RO         1631    i.gpio_config       sdio_sdcard.o
    0x08005480   0x08005480   0x00000010   Code   RO         4927    i.gpio_input_bit_get  gd32f4xx_gpio.o
    0x08005490   0x08005490   0x0000004e   Code   RO         4929    i.gpio_mode_set     gd32f4xx_gpio.o
    0x080054de   0x080054de   0x00000042   Code   RO         4931    i.gpio_output_options_set  gd32f4xx_gpio.o
    0x08005520   0x08005520   0x00000010   Code   RO         5029    i.i2c_ack_config    gd32f4xx_i2c.o
    0x08005530   0x08005530   0x000000e4   Code   RO         5033    i.i2c_clock_config  gd32f4xx_i2c.o
    0x08005614   0x08005614   0x00000058   Code   RO         5036    i.i2c_deinit        gd32f4xx_i2c.o
    0x0800566c   0x0800566c   0x00000010   Code   RO         5039    i.i2c_dma_config    gd32f4xx_i2c.o
    0x0800567c   0x0800567c   0x0000000a   Code   RO         5043    i.i2c_enable        gd32f4xx_i2c.o
    0x08005686   0x08005686   0x00000028   Code   RO         5044    i.i2c_flag_clear    gd32f4xx_i2c.o
    0x080056ae   0x080056ae   0x0000001e   Code   RO         5045    i.i2c_flag_get      gd32f4xx_i2c.o
    0x080056cc   0x080056cc   0x00000014   Code   RO         5050    i.i2c_master_addressing  gd32f4xx_i2c.o
    0x080056e0   0x080056e0   0x0000001c   Code   RO         5051    i.i2c_mode_addr_config  gd32f4xx_i2c.o
    0x080056fc   0x080056fc   0x0000000a   Code   RO         5064    i.i2c_start_on_bus  gd32f4xx_i2c.o
    0x08005706   0x08005706   0x0000000a   Code   RO         5065    i.i2c_stop_on_bus   gd32f4xx_i2c.o
    0x08005710   0x08005710   0x0000007c   Code   RO         8091    i.init_cycle_counter  perf_counter.o
    0x0800578c   0x0800578c   0x000000e4   Code   RO         2176    i.led_disp          led_app.o
    0x08005870   0x08005870   0x00000010   Code   RO         2177    i.led_task          led_app.o
    0x08005880   0x08005880   0x00000056   Code   RO          231    i.main              main.o
    0x080058d6   0x080058d6   0x00000026   Code   RO         1877    i.mem_cmp           ff.o
    0x080058fc   0x080058fc   0x0000001a   Code   RO         1878    i.mem_cpy           ff.o
    0x08005916   0x08005916   0x00000014   Code   RO         1879    i.mem_set           ff.o
    0x0800592a   0x0800592a   0x00000024   Code   RO         2315    i.memory_compare    sd_app.o
    0x0800594e   0x0800594e   0x00000072   Code   RO         1880    i.move_window       ff.o
    0x080059c0   0x080059c0   0x00000068   Code   RO         2280    i.my_printf         usart_app.o
    0x08005a28   0x08005a28   0x000000c4   Code   RO         5510    i.nvic_irq_enable   gd32f4xx_misc.o
    0x08005aec   0x08005aec   0x00000014   Code   RO         5511    i.nvic_priority_group_set  gd32f4xx_misc.o
    0x08005b00   0x08005b00   0x0000003c   Code   RO         2209    i.oled_printf       oled_app.o
    0x08005b3c   0x08005b3c   0x000000c4   Code   RO         2210    i.oled_task         oled_app.o
    0x08005c00   0x08005c00   0x00000010   Code   RO         8324    i.perfc_port_clear_system_timer_ovf_pending  perfc_port_default.o
    0x08005c10   0x08005c10   0x0000000c   Code   RO         8100    i.perfc_port_disable_global_interrupt  perf_counter.o
    0x08005c1c   0x08005c1c   0x00000012   Code   RO         8325    i.perfc_port_get_system_timer_elapsed  perfc_port_default.o
    0x08005c2e   0x08005c2e   0x00000002   PAD
    0x08005c30   0x08005c30   0x0000000c   Code   RO         8326    i.perfc_port_get_system_timer_freq  perfc_port_default.o
    0x08005c3c   0x08005c3c   0x0000000a   Code   RO         8327    i.perfc_port_get_system_timer_top  perfc_port_default.o
    0x08005c46   0x08005c46   0x00000052   Code   RO         8328    i.perfc_port_init_system_timer  perfc_port_default.o
    0x08005c98   0x08005c98   0x000000fc   Code   RO         8101    i.perfc_port_insert_to_system_timer_insert_ovf_handler  perf_counter.o
    0x08005d94   0x08005d94   0x00000010   Code   RO         8329    i.perfc_port_is_system_timer_ovf_pending  perfc_port_default.o
    0x08005da4   0x08005da4   0x0000000a   Code   RO         8102    i.perfc_port_resume_global_interrupt  perf_counter.o
    0x08005dae   0x08005dae   0x00000002   PAD
    0x08005db0   0x08005db0   0x00000014   Code   RO         5571    i.pmu_backup_write_enable  gd32f4xx_pmu.o
    0x08005dc4   0x08005dc4   0x00000084   Code   RO         2132    i.prv_btn_event     btn_app.o
    0x08005e48   0x08005e48   0x000000ac   Code   RO         2133    i.prv_btn_get_state  btn_app.o
    0x08005ef4   0x08005ef4   0x0000004c   Code   RO         1461    i.prv_get_combo_btn_by_key_id  ebtn.o
    0x08005f40   0x08005f40   0x00000374   Code   RO         1462    i.prv_process_btn   ebtn.o
    0x080062b4   0x080062b4   0x00000136   Code   RO         1881    i.put_fat           ff.o
    0x080063ea   0x080063ea   0x00000002   PAD
    0x080063ec   0x080063ec   0x00000084   Code   RO         1632    i.r1_error_check    sdio_sdcard.o
    0x08006470   0x08006470   0x000000ae   Code   RO         1633    i.r1_error_type_check  sdio_sdcard.o
    0x0800651e   0x0800651e   0x00000002   PAD
    0x08006520   0x08006520   0x00000050   Code   RO         1634    i.r2_error_check    sdio_sdcard.o
    0x08006570   0x08006570   0x0000003c   Code   RO         1635    i.r3_error_check    sdio_sdcard.o
    0x080065ac   0x080065ac   0x000000a8   Code   RO         1636    i.r6_error_check    sdio_sdcard.o
    0x08006654   0x08006654   0x00000050   Code   RO         1637    i.r7_error_check    sdio_sdcard.o
    0x080066a4   0x080066a4   0x00000014   Code   RO         5715    i.rcu_all_reset_flag_clear  gd32f4xx_rcu.o
    0x080066b8   0x080066b8   0x00000124   Code   RO         5723    i.rcu_clock_freq_get  gd32f4xx_rcu.o
    0x080067dc   0x080067dc   0x00000024   Code   RO         1638    i.rcu_config        sdio_sdcard.o
    0x08006800   0x08006800   0x00000024   Code   RO         5726    i.rcu_flag_get      gd32f4xx_rcu.o
    0x08006824   0x08006824   0x00000024   Code   RO         5739    i.rcu_osci_on       gd32f4xx_rcu.o
    0x08006848   0x08006848   0x0000015c   Code   RO         5740    i.rcu_osci_stab_wait  gd32f4xx_rcu.o
    0x080069a4   0x080069a4   0x00000024   Code   RO         5742    i.rcu_periph_clock_enable  gd32f4xx_rcu.o
    0x080069c8   0x080069c8   0x00000024   Code   RO         5745    i.rcu_periph_reset_disable  gd32f4xx_rcu.o
    0x080069ec   0x080069ec   0x00000024   Code   RO         5746    i.rcu_periph_reset_enable  gd32f4xx_rcu.o
    0x08006a10   0x08006a10   0x00000018   Code   RO         5751    i.rcu_rtc_clock_config  gd32f4xx_rcu.o
    0x08006a28   0x08006a28   0x00000068   Code   RO         1882    i.remove_chain      ff.o
    0x08006a90   0x08006a90   0x00000064   Code   RO         6027    i.rtc_current_time_get  gd32f4xx_rtc.o
    0x08006af4   0x08006af4   0x000000c4   Code   RO         6032    i.rtc_init          gd32f4xx_rtc.o
    0x08006bb8   0x08006bb8   0x00000048   Code   RO         6033    i.rtc_init_mode_enter  gd32f4xx_rtc.o
    0x08006c00   0x08006c00   0x00000014   Code   RO         6034    i.rtc_init_mode_exit  gd32f4xx_rtc.o
    0x08006c14   0x08006c14   0x00000060   Code   RO         6039    i.rtc_register_sync_wait  gd32f4xx_rtc.o
    0x08006c74   0x08006c74   0x0000003c   Code   RO         2360    i.rtc_task          rtc_app.o
    0x08006cb0   0x08006cb0   0x0000000c   Code   RO         2247    i.scheduler_init    scheduler.o
    0x08006cbc   0x08006cbc   0x00000058   Code   RO         2248    i.scheduler_run     scheduler.o
    0x08006d14   0x08006d14   0x00000218   Code   RO         1639    i.sd_block_read     sdio_sdcard.o
    0x08006f2c   0x08006f2c   0x00000320   Code   RO         1640    i.sd_block_write    sdio_sdcard.o
    0x0800724c   0x0800724c   0x00000094   Code   RO         1641    i.sd_bus_mode_config  sdio_sdcard.o
    0x080072e0   0x080072e0   0x000000fc   Code   RO         1642    i.sd_bus_width_config  sdio_sdcard.o
    0x080073dc   0x080073dc   0x000000a8   Code   RO         1643    i.sd_card_capacity_get  sdio_sdcard.o
    0x08007484   0x08007484   0x000002c0   Code   RO         1644    i.sd_card_information_get  sdio_sdcard.o
    0x08007744   0x08007744   0x0000011c   Code   RO         1645    i.sd_card_init      sdio_sdcard.o
    0x08007860   0x08007860   0x00000026   Code   RO         1646    i.sd_card_select_deselect  sdio_sdcard.o
    0x08007886   0x08007886   0x00000002   PAD
    0x08007888   0x08007888   0x000000b8   Code   RO         1647    i.sd_card_state_get  sdio_sdcard.o
    0x08007940   0x08007940   0x00000048   Code   RO         1648    i.sd_cardstatus_get  sdio_sdcard.o
    0x08007988   0x08007988   0x00000018   Code   RO         1649    i.sd_datablocksize_get  sdio_sdcard.o
    0x080079a0   0x080079a0   0x0000000e   Code   RO         2316    i.sd_fatfs_init     sd_app.o
    0x080079ae   0x080079ae   0x00000002   PAD
    0x080079b0   0x080079b0   0x0000022c   Code   RO         2317    i.sd_fatfs_test     sd_app.o
    0x08007bdc   0x08007bdc   0x00000046   Code   RO         1651    i.sd_init           sdio_sdcard.o
    0x08007c22   0x08007c22   0x00000002   PAD
    0x08007c24   0x08007c24   0x00000130   Code   RO         1652    i.sd_interrupts_process  sdio_sdcard.o
    0x08007d54   0x08007d54   0x0000029c   Code   RO         1654    i.sd_multiblocks_read  sdio_sdcard.o
    0x08007ff0   0x08007ff0   0x00000398   Code   RO         1655    i.sd_multiblocks_write  sdio_sdcard.o
    0x08008388   0x08008388   0x0000012c   Code   RO         1657    i.sd_power_on       sdio_sdcard.o
    0x080084b4   0x080084b4   0x0000015c   Code   RO         1658    i.sd_scr_get        sdio_sdcard.o
    0x08008610   0x08008610   0x00000018   Code   RO         1660    i.sd_transfer_mode_config  sdio_sdcard.o
    0x08008628   0x08008628   0x00000024   Code   RO         1662    i.sd_transfer_stop  sdio_sdcard.o
    0x0800864c   0x0800864c   0x0000001c   Code   RO         6284    i.sdio_bus_mode_set  gd32f4xx_sdio.o
    0x08008668   0x08008668   0x00000034   Code   RO         6291    i.sdio_clock_config  gd32f4xx_sdio.o
    0x0800869c   0x0800869c   0x00000014   Code   RO         6293    i.sdio_clock_enable  gd32f4xx_sdio.o
    0x080086b0   0x080086b0   0x0000000c   Code   RO         6294    i.sdio_command_index_get  gd32f4xx_sdio.o
    0x080086bc   0x080086bc   0x00000038   Code   RO         6295    i.sdio_command_response_config  gd32f4xx_sdio.o
    0x080086f4   0x080086f4   0x00000014   Code   RO         6297    i.sdio_csm_enable   gd32f4xx_sdio.o
    0x08008708   0x08008708   0x0000003c   Code   RO         6298    i.sdio_data_config  gd32f4xx_sdio.o
    0x08008744   0x08008744   0x0000000c   Code   RO         6300    i.sdio_data_read    gd32f4xx_sdio.o
    0x08008750   0x08008750   0x0000001c   Code   RO         6301    i.sdio_data_transfer_config  gd32f4xx_sdio.o
    0x0800876c   0x0800876c   0x0000000c   Code   RO         6302    i.sdio_data_write   gd32f4xx_sdio.o
    0x08008778   0x08008778   0x00000014   Code   RO         6303    i.sdio_deinit       gd32f4xx_sdio.o
    0x0800878c   0x0800878c   0x00000014   Code   RO         6304    i.sdio_dma_disable  gd32f4xx_sdio.o
    0x080087a0   0x080087a0   0x00000014   Code   RO         6305    i.sdio_dma_enable   gd32f4xx_sdio.o
    0x080087b4   0x080087b4   0x00000014   Code   RO         6306    i.sdio_dsm_disable  gd32f4xx_sdio.o
    0x080087c8   0x080087c8   0x00000014   Code   RO         6307    i.sdio_dsm_enable   gd32f4xx_sdio.o
    0x080087dc   0x080087dc   0x0000000c   Code   RO         6309    i.sdio_flag_clear   gd32f4xx_sdio.o
    0x080087e8   0x080087e8   0x00000014   Code   RO         6310    i.sdio_flag_get     gd32f4xx_sdio.o
    0x080087fc   0x080087fc   0x00000014   Code   RO         6311    i.sdio_hardware_clock_disable  gd32f4xx_sdio.o
    0x08008810   0x08008810   0x00000010   Code   RO         6313    i.sdio_interrupt_disable  gd32f4xx_sdio.o
    0x08008820   0x08008820   0x00000010   Code   RO         6314    i.sdio_interrupt_enable  gd32f4xx_sdio.o
    0x08008830   0x08008830   0x0000000c   Code   RO         6315    i.sdio_interrupt_flag_clear  gd32f4xx_sdio.o
    0x0800883c   0x0800883c   0x00000014   Code   RO         6316    i.sdio_interrupt_flag_get  gd32f4xx_sdio.o
    0x08008850   0x08008850   0x0000000c   Code   RO         6319    i.sdio_power_state_get  gd32f4xx_sdio.o
    0x0800885c   0x0800885c   0x0000000c   Code   RO         6320    i.sdio_power_state_set  gd32f4xx_sdio.o
    0x08008868   0x08008868   0x0000003c   Code   RO         6324    i.sdio_response_get  gd32f4xx_sdio.o
    0x080088a4   0x080088a4   0x0000001c   Code   RO         6329    i.sdio_wait_type_set  gd32f4xx_sdio.o
    0x080088c0   0x080088c0   0x00000016   Code   RO         6592    i.spi_dma_disable   gd32f4xx_spi.o
    0x080088d6   0x080088d6   0x00000016   Code   RO         6593    i.spi_dma_enable    gd32f4xx_spi.o
    0x080088ec   0x080088ec   0x0000000a   Code   RO         6594    i.spi_enable        gd32f4xx_spi.o
    0x080088f6   0x080088f6   0x00000002   PAD
    0x080088f8   0x080088f8   0x00000054   Code   RO          466    i.spi_flash_buffer_read  gd25qxx.o
    0x0800894c   0x0800894c   0x0000001c   Code   RO          469    i.spi_flash_init    gd25qxx.o
    0x08008968   0x08008968   0x00000054   Code   RO          471    i.spi_flash_read_id  gd25qxx.o
    0x080089bc   0x080089bc   0x000000fc   Code   RO          473    i.spi_flash_send_byte_dma  gd25qxx.o
    0x08008ab8   0x08008ab8   0x00000032   Code   RO         6604    i.spi_init          gd32f4xx_spi.o
    0x08008aea   0x08008aea   0x000000ca   Code   RO         1883    i.sync              ff.o
    0x08008bb4   0x08008bb4   0x00000110   Code   RO         8017    i.system_clock_240m_25m_hxtal  system_gd32f4xx.o
    0x08008cc4   0x08008cc4   0x00000008   Code   RO         8018    i.system_clock_config  system_gd32f4xx.o
    0x08008ccc   0x08008ccc   0x00000050   Code   RO          334    i.systick_config    systick.o
    0x08008d1c   0x08008d1c   0x000002ec   Code   RO          480    i.test_spi_flash    gd25qxx.o
    0x08009008   0x08009008   0x00000048   Code   RO          388    i.timer5_config     mcu_cmic_gd32f470vet6.o
    0x08009050   0x08009050   0x00000184   Code   RO         6934    i.timer_deinit      gd32f4xx_timer.o
    0x080091d4   0x080091d4   0x0000000a   Code   RO         6939    i.timer_enable      gd32f4xx_timer.o
    0x080091de   0x080091de   0x00000002   PAD
    0x080091e0   0x080091e0   0x00000098   Code   RO         6949    i.timer_init        gd32f4xx_timer.o
    0x08009278   0x08009278   0x00000010   Code   RO         6959    i.timer_master_output_trigger_source_select  gd32f4xx_timer.o
    0x08009288   0x08009288   0x00000016   Code   RO         6969    i.timer_struct_para_init  gd32f4xx_timer.o
    0x0800929e   0x0800929e   0x00000002   PAD
    0x080092a0   0x080092a0   0x00000034   Code   RO         2281    i.uart_task         usart_app.o
    0x080092d4   0x080092d4   0x00000074   Code   RO         8106    i.update_perf_counter  perf_counter.o
    0x08009348   0x08009348   0x000000e8   Code   RO         7599    i.usart_baudrate_set  gd32f4xx_usart.o
    0x08009430   0x08009430   0x0000000a   Code   RO         7603    i.usart_data_receive  gd32f4xx_usart.o
    0x0800943a   0x0800943a   0x00000008   Code   RO         7604    i.usart_data_transmit  gd32f4xx_usart.o
    0x08009442   0x08009442   0x00000002   PAD
    0x08009444   0x08009444   0x000000dc   Code   RO         7605    i.usart_deinit      gd32f4xx_usart.o
    0x08009520   0x08009520   0x00000014   Code   RO         7607    i.usart_dma_receive_config  gd32f4xx_usart.o
    0x08009534   0x08009534   0x0000000a   Code   RO         7609    i.usart_enable      gd32f4xx_usart.o
    0x0800953e   0x0800953e   0x0000001e   Code   RO         7611    i.usart_flag_get    gd32f4xx_usart.o
    0x0800955c   0x0800955c   0x0000001a   Code   RO         7619    i.usart_interrupt_enable  gd32f4xx_usart.o
    0x08009576   0x08009576   0x00000038   Code   RO         7621    i.usart_interrupt_flag_get  gd32f4xx_usart.o
    0x080095ae   0x080095ae   0x00000010   Code   RO         7636    i.usart_receive_config  gd32f4xx_usart.o
    0x080095be   0x080095be   0x00000010   Code   RO         7651    i.usart_transmit_config  gd32f4xx_usart.o
    0x080095ce   0x080095ce   0x0000002a   Code   RO         1884    i.validate          ff.o
    0x080095f8   0x080095f8   0x0000002c   Code   RO         8841    locale$$code        c_w.l(lc_numeric_c.o)
    0x08009624   0x08009624   0x0000002c   Code   RO         8898    locale$$code        c_w.l(lc_ctype_c.o)
    0x08009650   0x08009650   0x0000000c   Code   RO         8692    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x0800965c   0x0800965c   0x00000056   Code   RO         8554    x$fpl$f2d           fz_wm.l(f2d.o)
    0x080096b2   0x080096b2   0x0000008c   Code   RO         8694    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x0800973e   0x0800973e   0x0000000a   Code   RO         8777    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x08009748   0x08009748   0x00000004   Code   RO         8696    x$fpl$printf1       fz_wm.l(printf1.o)
    0x0800974c   0x0800974c   0x00000004   Code   RO         8698    x$fpl$printf2       fz_wm.l(printf2.o)
    0x08009750   0x08009750   0x00000000   Code   RO         8704    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x08009750   0x08009750   0x00000a98   Data   RO         1325    .constdata          oled.o
    0x0800a1e8   0x0800a1e8   0x0000000e   Data   RO         2134    .constdata          btn_app.o
    0x0800a1f6   0x0800a1f6   0x00000028   Data   RO         8494    .constdata          c_w.l(_printf_hex_int_ll_ptr.o)
    0x0800a21e   0x0800a21e   0x00000011   Data   RO         8514    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x0800a22f   0x0800a22f   0x00000001   PAD
    0x0800a230   0x0800a230   0x00000008   Data   RO         8605    .constdata          c_w.l(_printf_wctomb.o)
    0x0800a238   0x0800a238   0x00000026   Data   RO         8746    .constdata          c_w.l(_printf_fp_hex.o)
    0x0800a25e   0x0800a25e   0x00000002   PAD
    0x0800a260   0x0800a260   0x00000094   Data   RO         8805    .constdata          c_w.l(bigflt0.o)
    0x0800a2f4   0x0800a2f4   0x00000020   Data   RO         8943    Region$$Table       anon$$obj.o
    0x0800a314   0x0800a314   0x0000001c   Data   RO         8840    locale$$data        c_w.l(lc_numeric_c.o)
    0x0800a330   0x0800a330   0x00000110   Data   RO         8897    locale$$data        c_w.l(lc_ctype_c.o)
    0x0800a440   0x0800a440   0x00000004   Data   RO         8108    .init_array         perf_counter.o
    0x0800a444   0x0800a440   0x00000000   Data   RO         8549    .init_array         c_w.l(init_aeabi.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800a444, Size: 0x00001478, Max: 0x00030000, ABSOLUTE, COMPRESSED[0x00000074])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000004   Data   RW          335    .data               systick.o
    0x20000004   COMPRESSED   0x00000014   Data   RW          390    .data               mcu_cmic_gd32f470vet6.o
    0x20000018   COMPRESSED   0x00000016   Data   RW         1326    .data               oled.o
    0x2000002e   COMPRESSED   0x00000002   PAD
    0x20000030   COMPRESSED   0x00000024   Data   RW         1664    .data               sdio_sdcard.o
    0x20000054   COMPRESSED   0x00000006   Data   RW         1885    .data               ff.o
    0x2000005a   COMPRESSED   0x00000002   PAD
    0x2000005c   COMPRESSED   0x000000c4   Data   RW         2135    .data               btn_app.o
    0x20000120   COMPRESSED   0x00000007   Data   RW         2178    .data               led_app.o
    0x20000127   COMPRESSED   0x00000001   PAD
    0x20000128   COMPRESSED   0x0000004c   Data   RW         2249    .data               scheduler.o
    0x20000174   COMPRESSED   0x00000003   Data   RW         2283    .data               usart_app.o
    0x20000177   COMPRESSED   0x00000001   PAD
    0x20000178   COMPRESSED   0x00000010   Data   RW         2319    .data               sd_app.o
    0x20000188   COMPRESSED   0x00000004   Data   RW         8019    .data               system_gd32f4xx.o
    0x2000018c   COMPRESSED   0x00000004   PAD
    0x20000190   COMPRESSED   0x00000050   Data   RW         8107    .data               perf_counter.o
    0x200001e0        -       0x0000023c   Zero   RW          389    .bss                mcu_cmic_gd32f470vet6.o
    0x2000041c        -       0x0000003c   Zero   RW         1463    .bss                ebtn.o
    0x20000458        -       0x00000020   Zero   RW         1663    .bss                sdio_sdcard.o
    0x20000478        -       0x00000200   Zero   RW         2282    .bss                usart_app.o
    0x20000678        -       0x0000059c   Zero   RW         2318    .bss                sd_app.o
    0x20000c14        -       0x00000060   Zero   RW         8712    .bss                c_w.l(libspace.o)
    0x20000c74   COMPRESSED   0x00000004   PAD
    0x20000c78        -       0x00000400   Zero   RW         8004    HEAP                startup_gd32f450_470.o
    0x20001078        -       0x00000400   Zero   RW         8003    STACK               startup_gd32f450_470.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        20         10          0          0          0        475   adc_app.o
       354         46         14        196          0       3102   btn_app.o
       316          0          0          0          0       4872   diskio.o
      2070         26          0          0         60      20742   ebtn.o
      5142         46          0          6          0      25322   ff.o
      1196        516          0          0          0       4397   gd25qxx.o
       654         16          0          0          0       8606   gd32f4xx_adc.o
       198          6          0          0          0       3827   gd32f4xx_dac.o
      1332          8          0          0          0      10828   gd32f4xx_dma.o
       262          0          0          0          0       4329   gd32f4xx_gpio.o
       496         16          0          0          0       7387   gd32f4xx_i2c.o
       168         26          0          0          0     101521   gd32f4xx_it.o
       216         20          0          0          0       1472   gd32f4xx_misc.o
        20          6          0          0          0        542   gd32f4xx_pmu.o
       864         66          0          0          0       6816   gd32f4xx_rcu.o
       484         26          0          0          0       4082   gd32f4xx_rtc.o
       628        136          0          0          0      15486   gd32f4xx_sdio.o
       104          0          0          0          0       2991   gd32f4xx_spi.o
       588         44          0          0          0       3664   gd32f4xx_timer.o
       644         18          0          0          0       7509   gd32f4xx_usart.o
       244         14          0          7          0       1850   led_app.o
        86          0          0          0          0      11791   main.o
      1640        136          0         20        572       8494   mcu_cmic_gd32f470vet6.o
      1242         56       2712         22          0       7200   oled.o
       256         62          0          0          0       1926   oled_app.o
       882        112          4         80          0      26058   perf_counter.o
       154         18          0          0          0       6461   perfc_port_default.o
        60         24          0          0          0        503   rtc_app.o
       100         14          0         76          0       1868   scheduler.o
      1574        898          0         16       1436       3718   sd_app.o
      7134        352          0         36         32      33311   sdio_sdcard.o
        64         26        428          0       2048        932   startup_gd32f450_470.o
       694         30          0          4          0       4755   system_gd32f4xx.o
       164         24          0          4          0      32974   systick.o
        32         10          0          0          0        620   systick_wrapper_ual.o
       156         24          0          3        512       2023   usart_app.o

    ----------------------------------------------------------------------
     30278       <USER>       <GROUP>        480       4660     382454   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        40          0          0         10          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        44          6          0          0          0         84   __2sprintf.o
        90          0          0          0          0          0   __dczerorl2.o
         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        28          0          0          0          0          0   __scatter_zi.o
         6          0          0          0          0          0   _printf_a.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        40          0          0          0          0         68   _printf_charcount.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_e.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       764          8         38          0          0        100   _printf_fp_hex.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         6          0          0          0          0          0   _printf_g.o
       148          4         40          0          0        160   _printf_hex_int_ll_ptr.o
         6          0          0          0          0          0   _printf_i.o
       178          0          0          0          0         88   _printf_intcommon.o
        10          0          0          0          0          0   _printf_l.o
         6          0          0          0          0          0   _printf_lc.o
        10          0          0          0          0          0   _printf_ll.o
         6          0          0          0          0          0   _printf_lld.o
         6          0          0          0          0          0   _printf_lli.o
         6          0          0          0          0          0   _printf_llo.o
         6          0          0          0          0          0   _printf_llu.o
         6          0          0          0          0          0   _printf_llx.o
       124         16          0          0          0         92   _printf_longlong_dec.o
         6          0          0          0          0          0   _printf_ls.o
         6          0          0          0          0          0   _printf_n.o
         6          0          0          0          0          0   _printf_o.o
       112         10          0          0          0        124   _printf_oct_int_ll.o
         6          0          0          0          0          0   _printf_p.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        36          0          0          0          0         84   _printf_truncate.o
         6          0          0          0          0          0   _printf_u.o
        44          0          0          0          0        108   _printf_wchar.o
       188          6          8          0          0         92   _printf_wctomb.o
         6          0          0          0          0          0   _printf_x.o
        16          0          0          0          0         68   _snputc.o
        10          0          0          0          0         68   _sputc.o
        64          0          0          0          0         92   _wcrtomb.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        36         10          0          0          0         80   init_aeabi.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        38          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        72          0          0          0          0         76   llsdiv.o
       138          0          0          0          0         80   lludiv10.o
       238          0          0          0          0        100   lludivv7m.o
        88          0          0          0          0         76   memcmp.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        62          0          0          0          0         76   strlen.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        52          4          0          0          0         80   vsnprintf.o
        12          0          0          0          0        116   dretinf.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
        10          0          0          0          0        116   fpinit.o
         4          0          0          0          0        116   printf1.o
         4          0          0          0          0        116   printf2.o
         0          0          0          0          0          0   usenofp.o
        48          0          0          0          0        124   fpclassify.o

    ----------------------------------------------------------------------
      8030        <USER>        <GROUP>          0        100       5604   Library Totals
         8          0          3          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      7718        282        551          0         96       4752   c_w.l
       256          8          0          0          0        728   fz_wm.l
        48          0          0          0          0        124   m_wm.l

    ----------------------------------------------------------------------
      8030        <USER>        <GROUP>          0        100       5604   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     38308       3122       3744        480       4760     358206   Grand Totals
     38308       3122       3744        116       4760     358206   ELF Image Totals (compressed)
     38308       3122       3744        116          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                42052 (  41.07kB)
    Total RW  Size (RW Data + ZI Data)              5240 (   5.12kB)
    Total ROM Size (Code + RO Data + RW Data)      42168 (  41.18kB)

==============================================================================

