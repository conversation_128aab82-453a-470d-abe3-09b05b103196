{"name": "Project", "type": "ARM", "dependenceList": [], "srcDirs": [], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": [{"name": "User", "files": [{"path": "../USER/src/gd32f4xx_it.c"}, {"path": "../USER/src/main.c"}, {"path": "../USER/src/systick.c"}], "folders": []}, {"name": "Components", "files": [], "folders": [{"name": "bsp", "files": [{"path": "../Components/bsp/mcu_cmic_gd32f470vet6.c"}, {"path": "../Components/bsp/mcu_cmic_gd32f470vet6.h"}], "folders": []}, {"name": "gd32qxx", "files": [{"path": "../Components/gd25qxx/gd25qxx.c"}, {"path": "../Components/gd25qxx/lfs.c"}, {"path": "../Components/gd25qxx/lfs_port.c"}, {"path": "../Components/gd25qxx/lfs_util.c"}], "folders": []}, {"name": "oled", "files": [{"path": "../Components/oled/oled.c"}], "folders": []}, {"name": "ebtn", "files": [{"path": "../Components/ebtn/bit_array.h"}, {"path": "../Components/ebtn/ebtn.c"}, {"path": "../Components/ebtn/ebtn.h"}], "folders": []}, {"name": "sdio", "files": [{"path": "../Components/sdio/sdio_sdcard.c"}], "folders": []}, {"name": "fatfs", "files": [{"path": "../Components/fatfs/ff.c"}, {"path": "../Components/fatfs/diskio.c"}], "folders": []}]}, {"name": "APP", "files": [{"path": "../APP/btn_app.c"}, {"path": "../APP/led_app.c"}, {"path": "../APP/oled_app.c"}, {"path": "../APP/scheduler.c"}, {"path": "../APP/usart_app.c"}, {"path": "../APP/sd_app.c"}, {"path": "../APP/rtc_app.c"}, {"path": "../APP/rtc_app.h"}, {"path": "../APP/adc_app.c"}], "folders": []}, {"name": "Peripher<PERSON>", "files": [{"path": "../Libraries/Source/gd32f4xx_adc.c"}, {"path": "../Libraries/Source/gd32f4xx_can.c"}, {"path": "../Libraries/Source/gd32f4xx_crc.c"}, {"path": "../Libraries/Source/gd32f4xx_ctc.c"}, {"path": "../Libraries/Source/gd32f4xx_dac.c"}, {"path": "../Libraries/Source/gd32f4xx_dbg.c"}, {"path": "../Libraries/Source/gd32f4xx_dci.c"}, {"path": "../Libraries/Source/gd32f4xx_dma.c"}, {"path": "../Libraries/Source/gd32f4xx_enet.c"}, {"path": "../Libraries/Source/gd32f4xx_exmc.c"}, {"path": "../Libraries/Source/gd32f4xx_exti.c"}, {"path": "../Libraries/Source/gd32f4xx_fmc.c"}, {"path": "../Libraries/Source/gd32f4xx_fwdgt.c"}, {"path": "../Libraries/Source/gd32f4xx_gpio.c"}, {"path": "../Libraries/Source/gd32f4xx_i2c.c"}, {"path": "../Libraries/Source/gd32f4xx_ipa.c"}, {"path": "../Libraries/Source/gd32f4xx_iref.c"}, {"path": "../Libraries/Source/gd32f4xx_misc.c"}, {"path": "../Libraries/Source/gd32f4xx_pmu.c"}, {"path": "../Libraries/Source/gd32f4xx_rcu.c"}, {"path": "../Libraries/Source/gd32f4xx_rtc.c"}, {"path": "../Libraries/Source/gd32f4xx_sdio.c"}, {"path": "../Libraries/Source/gd32f4xx_spi.c"}, {"path": "../Libraries/Source/gd32f4xx_syscfg.c"}, {"path": "../Libraries/Source/gd32f4xx_timer.c"}, {"path": "../Libraries/Source/gd32f4xx_tli.c"}, {"path": "../Libraries/Source/gd32f4xx_trng.c"}, {"path": "../Libraries/Source/gd32f4xx_usart.c"}, {"path": "../Libraries/Source/gd32f4xx_wwdgt.c"}], "folders": []}, {"name": "Startup", "files": [{"path": "../Libraries/startup_gd32f450_470.s"}], "folders": []}, {"name": "CMSIS", "files": [{"path": "../Driver/CMSIS/GD/GD32F4xx/Source/system_gd32f4xx.c"}], "folders": []}, {"name": "Doc", "files": [{"path": "../readme.txt"}, {"path": "../io_multiplexing_table.txt"}], "folders": []}, {"name": "::CMSIS", "files": [], "folders": []}, {"name": "::Utilities", "files": [], "folders": []}]}, "outDir": "build", "deviceName": null, "packDir": null, "miscInfo": {"uid": "f0aebca1e274bb253205c1bdda210a42"}, "targets": {"McuSTUDIO_F470VET6": {"excludeList": [], "toolchain": "AC5", "compileConfig": {"cpuType": "Cortex-M4", "floatingPointHardware": "single", "scatterFilePath": "", "useCustomScatterFile": false, "storageLayout": {"RAM": [{"tag": "RAM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "IRAM", "id": 1, "mem": {"startAddr": "0x20000000", "size": "0x30000"}, "isChecked": true, "noInit": false}, {"tag": "IRAM", "id": 2, "mem": {"startAddr": "0x10000000", "size": "0x10000"}, "isChecked": false, "noInit": false}], "ROM": [{"tag": "ROM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "IROM", "id": 1, "mem": {"startAddr": "0x8000000", "size": "0x80000"}, "isChecked": true, "isStartup": true}, {"tag": "IROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}]}, "options": "null"}, "uploader": "JLink", "uploadConfig": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "null", "cpuName": "null"}, "proType": 1, "speed": 8000, "otherCmds": ""}, "uploadConfigMap": {}, "custom_dep": {"name": "default", "incList": ["../Libraries/Include", "../USER/inc", "../Driver/CMSIS/GD/GD32F4xx/Include", "../Components/bsp", "../Components/oled", "../Components/gd25qxx", "../Components/ebtn", "../Components/sdio", "../Components/fatfs", "../APP", ".cmsis/include", "RTE/_McuSTUDIO_F470VET6"], "libList": [], "defineList": ["USE_STDPERIPH_DRIVER", "GD32F470"]}, "builderOptions": {"AC5": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [{"name": "[Copy linker output for Keil User Commands]", "command": "mkdir ${KEIL_OUTPUT_DIR} & copy \"${OutDir}\\${ProjectName}.axf\" \"${KEIL_OUTPUT_DIR}\\${ProjectName}.axf\"", "disable": false, "abortAfterFailed": true}, {"name": "./keil5_disp_size_bar.exe", "command": "./keil5_disp_size_bar.exe", "disable": false, "abortAfterFailed": true}], "global": {"use-microLIB": false, "output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-0", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "all-warnings"}, "asm-compiler": {}, "linker": {"output-format": "elf", "xo-base": "", "ro-base": "0x08000000", "rw-base": "0x20000000"}}}}}, "version": "3.5"}